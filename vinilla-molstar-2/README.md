# Mol* SDF/MOL2 文件可视化器

这是一个基于Mol*的分子结构可视化工具，专门用于显示SDF和MOL2格式的分子文件。

## 功能特性

- ✅ 支持SDF和MOL2文件格式
- ✅ 拖拽上传文件
- ✅ 自动居中分子结构
- ✅ 原子标记功能（显示'x'字符）
- ✅ 分子信息显示（原子数、键数等）
- ✅ 响应式设计，支持移动设备

## 技术实现

- **前端框架**: 原生JavaScript + HTML5 + CSS3
- **分子可视化**: Mol* (使用PluginContext API)
- **开发服务器**: http-server
- **样式**: 现代CSS3，渐变背景，毛玻璃效果

## 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 启动开发服务器
```bash
npm run dev
```

### 3. 打开浏览器
访问 http://localhost:8080

## 使用方法

1. **上传文件**: 
   - 点击"选择文件"按钮或直接拖拽SDF/MOL2文件到上传区域
   - 支持的格式：`.sdf`, `.mol2`

2. **查看分子结构**:
   - 文件上传后会自动加载并显示分子结构
   - 结构会自动居中显示

3. **控制功能**:
   - **居中分子**: 重新将分子居中显示
   - **切换原子标记**: 显示/隐藏原子上的'x'标记
   - **重置视图**: 重置相机视角

4. **分子信息**:
   - 右上角会显示分子的基本信息
   - 包括原子数量、键数量等

## 项目结构

```
vinilla-molstar-2/
├── index.html          # 主页面
├── style.css           # 样式文件
├── main.js             # 主要JavaScript逻辑
├── package.json        # 项目配置
├── test-molecule.sdf   # 测试用SDF文件
└── README.md           # 说明文档
```

## 核心技术细节

### Mol* 集成
- 使用 `molstar.Viewer.create()` 创建查看器实例
- 通过 `viewer.loadStructureFromData()` 加载分子数据
- 使用 `plugin.managers.camera` 控制相机视角

### 文件处理
- 使用 `FileReader` API 读取本地文件
- 支持拖拽上传功能
- 文件格式验证

### 用户界面
- 响应式设计，适配不同屏幕尺寸
- 现代化UI设计，包含加载动画和错误提示
- 渐变背景和毛玻璃效果

## 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 开发说明

### 添加新的文件格式支持
在 `loadStructure` 方法中添加新的格式处理：

```javascript
if (format === 'newformat') {
    trajectory = await this.plugin.builders.structure.parseTrajectory(data, 'newformat');
}
```

### 自定义样式
修改 `style.css` 文件中的CSS变量来调整主题颜色：

```css
:root {
    --primary-color: #4CAF50;
    --background-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
```

## 故障排除

### 常见问题

1. **Mol*初始化失败**
   - 确保网络连接正常，Mol*库能正常加载
   - 检查浏览器控制台是否有JavaScript错误

2. **文件加载失败**
   - 确认文件格式正确（.sdf 或 .mol2）
   - 检查文件内容是否符合标准格式

3. **显示异常**
   - 尝试刷新页面
   - 检查浏览器是否支持WebGL

### 调试模式
在浏览器控制台中可以访问以下调试对象：
- `viewer` - 全局查看器实例
- `viewer.plugin` - Mol* 插件实例

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 更新日志

### v1.0.0 (2025-09-05)
- 初始版本发布
- 支持SDF和MOL2文件格式
- 基本的分子可视化功能
- 原子标记功能
- 响应式用户界面
