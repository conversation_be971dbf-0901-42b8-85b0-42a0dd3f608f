<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mol* SDF/MOL2 文件可视化器</title>
    <link rel="stylesheet" href="style.css">
    <!-- Mol* CSS -->
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/molstar@4.2.0/build/viewer/molstar.css" />
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>Mol* SDF/MOL2 文件可视化器</h1>
            <p>上传您的SDF或MOL2文件进行分子结构可视化</p>
        </header>

        <div class="upload-section">
            <div class="upload-area" id="uploadArea">
                <div class="upload-content">
                    <div class="upload-icon">📁</div>
                    <p>拖拽文件到此处或点击选择文件</p>
                    <p class="file-types">支持格式: .sdf, .mol2</p>
                    <input type="file" id="fileInput" accept=".sdf,.mol2" style="display: none;">
                    <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                        选择文件
                    </button>
                </div>
            </div>
            
            <div class="file-info" id="fileInfo" style="display: none;">
                <div class="file-details">
                    <span class="file-name" id="fileName"></span>
                    <span class="file-size" id="fileSize"></span>
                </div>
                <button class="clear-btn" id="clearBtn">清除</button>
            </div>
        </div>

        <div class="controls">
            <button id="centerBtn" class="control-btn" disabled>居中分子</button>
            <button id="toggleLabelsBtn" class="control-btn" disabled>切换原子标记</button>
            <button id="resetViewBtn" class="control-btn" disabled>重置视图</button>
        </div>

        <div class="viewer-container">
            <div id="molstar-parent" class="molstar-parent">
                <canvas id="molstar-canvas" class="molstar-canvas"></canvas>
            </div>
            
            <div class="loading" id="loading" style="display: none;">
                <div class="spinner"></div>
                <p>正在加载分子结构...</p>
            </div>
            
            <div class="error-message" id="errorMessage" style="display: none;">
                <p id="errorText"></p>
                <button onclick="hideError()">关闭</button>
            </div>
        </div>

        <div class="info-panel" id="infoPanel" style="display: none;">
            <h3>分子信息</h3>
            <div class="info-content">
                <div class="info-item">
                    <label>原子数量:</label>
                    <span id="atomCount">-</span>
                </div>
                <div class="info-item">
                    <label>键数量:</label>
                    <span id="bondCount">-</span>
                </div>
                <div class="info-item">
                    <label>分子式:</label>
                    <span id="molecularFormula">-</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Mol* JavaScript -->
    <script type="text/javascript" src="https://unpkg.com/molstar@4.2.0/build/viewer/molstar.js"></script>
    <script src="main.js"></script>
</body>
</html>
