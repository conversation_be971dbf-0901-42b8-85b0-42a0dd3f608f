// Mol* SDF/MOL2 文件可视化器
class MolstarViewer {
    constructor() {
        this.plugin = null;
        this.currentStructure = null;
        this.labelsVisible = false;
        this.labelRepresentation = null;
        this.init();
    }

    async init() {
        try {
            // 等待Molstar加载完成
            if (typeof molstar === 'undefined') {
                throw new Error('Molstar library not loaded');
            }

            // 等待一下确保所有模块都加载完成
            await new Promise(resolve => setTimeout(resolve, 500));

            console.log('Available molstar properties:', Object.keys(molstar));

            // 尝试使用Viewer.create方法，这是浏览器环境推荐的方式
            const canvas = document.getElementById('molstar-canvas');
            const parent = document.getElementById('molstar-parent');

            // 隐藏canvas，我们将使用Viewer.create
            canvas.style.display = 'none';

            // 使用Viewer.create创建查看器
            this.viewer = await molstar.Viewer.create(parent, {
                layoutIsExpanded: false,
                layoutShowControls: false,
                layoutShowRemoteState: false,
                layoutShowSequence: false,
                layoutShowLog: false,
                layoutShowLeftPanel: false,
                viewportShowExpand: false,
                viewportShowSelectionMode: false,
                viewportShowAnimation: false
            });

            this.plugin = this.viewer.plugin;

            console.log('Mol* viewer initialized successfully');
            this.setupEventListeners();
        } catch (error) {
            console.error('Failed to initialize Mol* viewer:', error);
            this.showError('初始化Mol*查看器失败: ' + error.message);
        }
    }

    setupEventListeners() {
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        const clearBtn = document.getElementById('clearBtn');
        const centerBtn = document.getElementById('centerBtn');
        const toggleLabelsBtn = document.getElementById('toggleLabelsBtn');
        const resetViewBtn = document.getElementById('resetViewBtn');

        // 文件选择事件
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleFile(e.target.files[0]);
            }
        });

        // 拖拽事件
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            if (e.dataTransfer.files.length > 0) {
                this.handleFile(e.dataTransfer.files[0]);
            }
        });

        // 控制按钮事件
        clearBtn.addEventListener('click', () => this.clearFile());
        centerBtn.addEventListener('click', () => this.centerMolecule());
        toggleLabelsBtn.addEventListener('click', () => this.toggleAtomLabels());
        resetViewBtn.addEventListener('click', () => this.resetView());
    }

    async handleFile(file) {
        // 验证文件类型
        const validExtensions = ['.sdf', '.mol2'];
        const fileName = file.name.toLowerCase();
        const isValid = validExtensions.some(ext => fileName.endsWith(ext));

        if (!isValid) {
            this.showError('不支持的文件格式。请选择SDF或MOL2文件。');
            return;
        }

        // 显示文件信息
        this.showFileInfo(file);
        this.showLoading(true);

        try {
            // 读取文件内容
            const fileContent = await this.readFileAsText(file);
            
            // 确定文件格式
            const format = fileName.endsWith('.sdf') ? 'sdf' : 'mol2';
            
            // 加载分子结构
            await this.loadStructure(fileContent, format);
            
            // 启用控制按钮
            this.enableControls(true);
            
            // 显示分子信息
            this.showMoleculeInfo();
            
        } catch (error) {
            console.error('Error loading file:', error);
            this.showError('加载文件失败: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = () => reject(new Error('文件读取失败'));
            reader.readAsText(file);
        });
    }

    async loadStructure(content, format) {
        try {
            // 清除之前的结构
            if (this.currentStructure) {
                await this.viewer.clear();
            }

            // 使用Viewer的loadStructureFromData方法
            const isBinary = false; // SDF和MOL2都是文本格式

            try {
                // 尝试使用loadStructureFromData
                await this.viewer.loadStructureFromData(content, format, isBinary, {
                    label: `Molecule (${format.toUpperCase()})`
                });

                this.currentStructure = true; // 标记已加载结构

            } catch (loadError) {
                console.warn('loadStructureFromData failed, trying alternative method:', loadError);

                // 备用方法：使用plugin builders
                const data = await this.plugin.builders.data.rawData({
                    data: content,
                    label: `Molecule (${format.toUpperCase()})`
                }, { state: { isGhost: true } });

                // 解析轨迹
                const trajectory = await this.plugin.builders.structure.parseTrajectory(data, format);

                // 应用默认预设
                const structure = await this.plugin.builders.structure.hierarchy.applyPreset(
                    trajectory,
                    'default'
                );

                this.currentStructure = structure;
            }

            // 自动居中分子
            setTimeout(() => {
                this.centerMolecule();
            }, 500);

            console.log('Structure loaded successfully');

        } catch (error) {
            console.error('Error in loadStructure:', error);
            throw new Error('解析分子结构失败: ' + error.message);
        }
    }

    centerMolecule() {
        if (!this.currentStructure || !this.plugin) return;

        try {
            // 使用简单的相机重置和聚焦
            this.plugin.managers.camera.reset();

            // 尝试聚焦到所有结构
            const structures = this.plugin.managers.structure.hierarchy.current.structures;
            if (structures.length > 0) {
                const structure = structures[0];
                if (structure?.cell?.obj?.data) {
                    // 创建整个结构的Loci
                    const structureData = structure.cell.obj.data;
                    const loci = molstar.StructureElement.Loci.all(structureData);
                    this.plugin.managers.camera.focusLoci(loci);
                }
            }

            console.log('Molecule centered');
        } catch (error) {
            console.error('Error centering molecule:', error);
            // 备用方法：重置相机
            try {
                this.plugin.managers.camera.reset();
            } catch (resetError) {
                console.error('Error resetting camera:', resetError);
            }
        }
    }

    async toggleAtomLabels() {
        if (!this.currentStructure || !this.plugin) return;

        try {
            if (this.labelsVisible) {
                // 隐藏标签
                if (this.labelRepresentation) {
                    await this.plugin.managers.structure.component.removeRepresentations(
                        this.currentStructure, 
                        [this.labelRepresentation]
                    );
                    this.labelRepresentation = null;
                }
                this.labelsVisible = false;
                document.getElementById('toggleLabelsBtn').textContent = '显示原子标记';
            } else {
                // 显示标签
                await this.addAtomLabels();
                this.labelsVisible = true;
                document.getElementById('toggleLabelsBtn').textContent = '隐藏原子标记';
            }
        } catch (error) {
            console.error('Error toggling atom labels:', error);
        }
    }

    async addAtomLabels() {
        if (!this.currentStructure || !this.plugin) return;

        try {
            // 添加文本标签表示 - 使用正确的API
            const labelRepr = await this.plugin.builders.structure.representation.addRepresentation(
                this.currentStructure,
                {
                    type: 'label',
                    typeParams: {
                        level: 'atom'
                    },
                    colorParams: {
                        name: 'uniform',
                        params: { value: 0x000000 }  // 黑色标签
                    }
                }
            );

            this.labelRepresentation = labelRepr;
            console.log('Atom labels added');
        } catch (error) {
            console.error('Error adding atom labels:', error);
            // 如果标签添加失败，尝试球棍模型
            try {
                const ballStickRepr = await this.plugin.builders.structure.representation.addRepresentation(
                    this.currentStructure,
                    {
                        type: 'ball-and-stick',
                        typeParams: {},
                        colorParams: {
                            name: 'element-symbol',
                            params: {}
                        }
                    }
                );
                this.labelRepresentation = ballStickRepr;
                console.log('Ball-and-stick representation added as alternative');
            } catch (altError) {
                console.error('Error adding alternative representation:', altError);
                this.showError('添加原子标记失败，该功能可能不支持当前文件格式');
            }
        }
    }

    resetView() {
        if (!this.plugin) return;

        try {
            // 重置相机视图
            this.plugin.managers.camera.reset();
            
            // 重新居中分子
            setTimeout(() => {
                this.centerMolecule();
            }, 100);
            
            console.log('View reset');
        } catch (error) {
            console.error('Error resetting view:', error);
        }
    }

    showFileInfo(file) {
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const fileInfo = document.getElementById('fileInfo');
        const uploadArea = document.getElementById('uploadArea');

        fileName.textContent = file.name;
        fileSize.textContent = this.formatFileSize(file.size);
        
        uploadArea.style.display = 'none';
        fileInfo.style.display = 'flex';
    }

    clearFile() {
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const uploadArea = document.getElementById('uploadArea');
        const infoPanel = document.getElementById('infoPanel');

        fileInput.value = '';
        fileInfo.style.display = 'none';
        uploadArea.style.display = 'block';
        infoPanel.style.display = 'none';

        this.enableControls(false);

        // 清除Mol*中的结构
        if (this.viewer) {
            this.viewer.clear();
        }
        
        this.currentStructure = null;
        this.labelsVisible = false;
        this.labelRepresentation = null;
    }

    enableControls(enabled) {
        const buttons = ['centerBtn', 'toggleLabelsBtn', 'resetViewBtn'];
        buttons.forEach(id => {
            document.getElementById(id).disabled = !enabled;
        });
    }

    showMoleculeInfo() {
        if (!this.currentStructure) return;

        try {
            const structureData = this.currentStructure.cell?.obj?.data;
            if (!structureData) return;

            // 计算原子和键的数量
            let atomCount = 0;
            let bondCount = 0;

            // 遍历结构获取统计信息 - 使用正确的API
            try {
                atomCount = structureData.elementCount;
                bondCount = structureData.bonds?.edgeCount || 0;
            } catch (countError) {
                console.warn('Could not get exact counts, using fallback method');
                // 备用计数方法
                atomCount = structureData.atomicHierarchy?.atoms?.count || 0;
                bondCount = 0; // 暂时设为0，因为键计数比较复杂
            }

            // 更新信息面板
            document.getElementById('atomCount').textContent = atomCount || '未知';
            document.getElementById('bondCount').textContent = bondCount || '未知';
            document.getElementById('molecularFormula').textContent = '计算中...';
            document.getElementById('infoPanel').style.display = 'block';

        } catch (error) {
            console.error('Error showing molecule info:', error);
            // 显示基本信息
            document.getElementById('atomCount').textContent = '未知';
            document.getElementById('bondCount').textContent = '未知';
            document.getElementById('molecularFormula').textContent = '未知';
            document.getElementById('infoPanel').style.display = 'block';
        }
    }

    showLoading(show) {
        document.getElementById('loading').style.display = show ? 'block' : 'none';
    }

    showError(message) {
        document.getElementById('errorText').textContent = message;
        document.getElementById('errorMessage').style.display = 'block';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// 全局函数
function hideError() {
    document.getElementById('errorMessage').style.display = 'none';
}

// 初始化应用
let viewer;
document.addEventListener('DOMContentLoaded', () => {
    viewer = new MolstarViewer();
});
