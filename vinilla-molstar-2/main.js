// Mol* SDF/MOL2 文件可视化器
class MolstarViewer {
    constructor() {
        this.plugin = null;
        this.currentStructure = null;
        this.labelsVisible = false;
        this.labelRepresentation = null;
        this.init();
    }

    async init() {
        try {
            // 初始化 PluginContext
            const spec = molstar.DefaultPluginSpec();
            this.plugin = new molstar.PluginContext(spec);
            await this.plugin.init();

            // 获取canvas和父元素
            const canvas = document.getElementById('molstar-canvas');
            const parent = document.getElementById('molstar-parent');

            if (!this.plugin.initViewer(canvas, parent)) {
                throw new Error('Failed to initialize Mol* viewer');
            }

            console.log('Mol* viewer initialized successfully');
            this.setupEventListeners();
        } catch (error) {
            console.error('Failed to initialize Mol* viewer:', error);
            this.showError('初始化Mol*查看器失败: ' + error.message);
        }
    }

    setupEventListeners() {
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        const clearBtn = document.getElementById('clearBtn');
        const centerBtn = document.getElementById('centerBtn');
        const toggleLabelsBtn = document.getElementById('toggleLabelsBtn');
        const resetViewBtn = document.getElementById('resetViewBtn');

        // 文件选择事件
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleFile(e.target.files[0]);
            }
        });

        // 拖拽事件
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            if (e.dataTransfer.files.length > 0) {
                this.handleFile(e.dataTransfer.files[0]);
            }
        });

        // 控制按钮事件
        clearBtn.addEventListener('click', () => this.clearFile());
        centerBtn.addEventListener('click', () => this.centerMolecule());
        toggleLabelsBtn.addEventListener('click', () => this.toggleAtomLabels());
        resetViewBtn.addEventListener('click', () => this.resetView());
    }

    async handleFile(file) {
        // 验证文件类型
        const validExtensions = ['.sdf', '.mol2'];
        const fileName = file.name.toLowerCase();
        const isValid = validExtensions.some(ext => fileName.endsWith(ext));

        if (!isValid) {
            this.showError('不支持的文件格式。请选择SDF或MOL2文件。');
            return;
        }

        // 显示文件信息
        this.showFileInfo(file);
        this.showLoading(true);

        try {
            // 读取文件内容
            const fileContent = await this.readFileAsText(file);
            
            // 确定文件格式
            const format = fileName.endsWith('.sdf') ? 'sdf' : 'mol2';
            
            // 加载分子结构
            await this.loadStructure(fileContent, format);
            
            // 启用控制按钮
            this.enableControls(true);
            
            // 显示分子信息
            this.showMoleculeInfo();
            
        } catch (error) {
            console.error('Error loading file:', error);
            this.showError('加载文件失败: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = () => reject(new Error('文件读取失败'));
            reader.readAsText(file);
        });
    }

    async loadStructure(content, format) {
        try {
            // 清除之前的结构
            if (this.currentStructure) {
                await this.plugin.clear();
            }

            // 创建数据对象
            const data = await this.plugin.builders.data.rawData({
                data: content,
                label: `Molecule (${format.toUpperCase()})`
            });

            // 解析轨迹
            const trajectory = await this.plugin.builders.structure.parseTrajectory(
                data, 
                format
            );

            // 应用默认预设
            const structure = await this.plugin.builders.structure.hierarchy.applyPreset(
                trajectory, 
                'default'
            );

            this.currentStructure = structure;

            // 自动居中分子
            setTimeout(() => {
                this.centerMolecule();
            }, 500);

            console.log('Structure loaded successfully');

        } catch (error) {
            console.error('Error in loadStructure:', error);
            throw new Error('解析分子结构失败');
        }
    }

    centerMolecule() {
        if (!this.currentStructure || !this.plugin) return;

        try {
            // 获取结构数据
            const structureData = this.currentStructure.cell?.obj?.data;
            if (!structureData) return;

            // 创建整个结构的Loci
            const loci = molstar.Structure.toStructureElementLoci(structureData);
            
            // 聚焦到结构
            this.plugin.managers.camera.focusLoci(loci);
            
            console.log('Molecule centered');
        } catch (error) {
            console.error('Error centering molecule:', error);
        }
    }

    async toggleAtomLabels() {
        if (!this.currentStructure || !this.plugin) return;

        try {
            if (this.labelsVisible) {
                // 隐藏标签
                if (this.labelRepresentation) {
                    await this.plugin.managers.structure.component.removeRepresentations(
                        this.currentStructure, 
                        [this.labelRepresentation]
                    );
                    this.labelRepresentation = null;
                }
                this.labelsVisible = false;
                document.getElementById('toggleLabelsBtn').textContent = '显示原子标记';
            } else {
                // 显示标签
                await this.addAtomLabels();
                this.labelsVisible = true;
                document.getElementById('toggleLabelsBtn').textContent = '隐藏原子标记';
            }
        } catch (error) {
            console.error('Error toggling atom labels:', error);
        }
    }

    async addAtomLabels() {
        if (!this.currentStructure || !this.plugin) return;

        try {
            // 添加文本标签表示
            const labelRepr = await this.plugin.builders.structure.representation.addRepresentation(
                this.currentStructure,
                {
                    type: 'label',
                    typeParams: {
                        level: 'atom',
                        text: 'x'  // 使用 'x' 字符作为标签
                    },
                    colorParams: {
                        name: 'uniform',
                        params: { value: 0x000000 }  // 黑色标签
                    }
                }
            );

            this.labelRepresentation = labelRepr;
            console.log('Atom labels added');
        } catch (error) {
            console.error('Error adding atom labels:', error);
            // 如果标签添加失败，尝试其他方法
            this.showError('添加原子标记失败，该功能可能不支持当前文件格式');
        }
    }

    resetView() {
        if (!this.plugin) return;

        try {
            // 重置相机视图
            this.plugin.managers.camera.reset();
            
            // 重新居中分子
            setTimeout(() => {
                this.centerMolecule();
            }, 100);
            
            console.log('View reset');
        } catch (error) {
            console.error('Error resetting view:', error);
        }
    }

    showFileInfo(file) {
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const fileInfo = document.getElementById('fileInfo');
        const uploadArea = document.getElementById('uploadArea');

        fileName.textContent = file.name;
        fileSize.textContent = this.formatFileSize(file.size);
        
        uploadArea.style.display = 'none';
        fileInfo.style.display = 'flex';
    }

    clearFile() {
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const uploadArea = document.getElementById('uploadArea');
        const infoPanel = document.getElementById('infoPanel');

        fileInput.value = '';
        fileInfo.style.display = 'none';
        uploadArea.style.display = 'block';
        infoPanel.style.display = 'none';

        this.enableControls(false);

        // 清除Mol*中的结构
        if (this.plugin) {
            this.plugin.clear();
        }
        
        this.currentStructure = null;
        this.labelsVisible = false;
        this.labelRepresentation = null;
    }

    enableControls(enabled) {
        const buttons = ['centerBtn', 'toggleLabelsBtn', 'resetViewBtn'];
        buttons.forEach(id => {
            document.getElementById(id).disabled = !enabled;
        });
    }

    showMoleculeInfo() {
        if (!this.currentStructure) return;

        try {
            const structureData = this.currentStructure.cell?.obj?.data;
            if (!structureData) return;

            // 计算原子和键的数量
            let atomCount = 0;
            let bondCount = 0;

            // 遍历结构获取统计信息
            molstar.Structure.eachAtomicHierarchyElement(structureData, {
                atom: () => atomCount++
            });

            // 获取键信息（这是一个简化的计算）
            bondCount = structureData.bonds?.edgeCount || 0;

            // 更新信息面板
            document.getElementById('atomCount').textContent = atomCount;
            document.getElementById('bondCount').textContent = bondCount;
            document.getElementById('molecularFormula').textContent = '计算中...';
            document.getElementById('infoPanel').style.display = 'block';

        } catch (error) {
            console.error('Error showing molecule info:', error);
        }
    }

    showLoading(show) {
        document.getElementById('loading').style.display = show ? 'block' : 'none';
    }

    showError(message) {
        document.getElementById('errorText').textContent = message;
        document.getElementById('errorMessage').style.display = 'block';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// 全局函数
function hideError() {
    document.getElementById('errorMessage').style.display = 'none';
}

// 初始化应用
let viewer;
document.addEventListener('DOMContentLoaded', () => {
    viewer = new MolstarViewer();
});
