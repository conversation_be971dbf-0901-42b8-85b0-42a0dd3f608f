{"name": "@types/benchmark", "version": "2.1.5", "description": "TypeScript definitions for benchmark", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/benchmark", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://asana.com"}, {"name": "<PERSON>", "githubUsername": "fishcharlie", "url": "https://github.com/fishcharlie"}, {"name": "<PERSON>", "githubUsername": "blair", "url": "https://github.com/blair"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/benchmark"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "0d65b73ff2fb48fbce07355999d5c6efc8c4a77d00f3d189716974dc860242f7", "typeScriptVersion": "4.5"}