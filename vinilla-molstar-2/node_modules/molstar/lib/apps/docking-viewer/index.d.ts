/**
 * Copyright (c) 2018-2020 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 */
import { BuiltInTrajectoryFormat } from '../../mol-plugin-state/formats/trajectory';
import { PluginUIContext } from '../../mol-plugin-ui/context';
import { Color } from '../../mol-util/color';
import '../../mol-util/polyfill';
import './index.html';
import '../../mol-plugin-ui/skin/light.scss';
export { PLUGIN_VERSION as version } from '../../mol-plugin/version';
export { setDebugMode, setProductionMode } from '../../mol-util/debug';
export { Viewer as DockingViewer };
declare class Viewer {
    plugin: PluginUIContext;
    constructor(plugin: PluginUIContext);
    static create(elementOrId: string | HTMLElement, colors?: Color[], showButtons?: boolean): Promise<Viewer>;
    loadStructuresFromUrlsAndMerge(sources: {
        url: string;
        format: BuiltInTrajectoryFormat;
        isBinary?: boolean;
    }[]): Promise<void>;
}
