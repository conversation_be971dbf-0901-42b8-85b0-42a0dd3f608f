@use "sass:color";

@use '../../mol-plugin-ui/skin/base/colors' with (
    $default-background:         #2D3E50,
    $font-color:                 #EDF1F2,
    $hover-font-color:           #3B9AD9,
    $entity-current-font-color:  #FFFFFF,
    $msp-btn-remove-background:      #BF3A31,
    $msp-btn-remove-hover-font-color:#ffffff,
    $msp-btn-commit-on-font-color:   #ffffff,
    $entity-badge-font-color:    #ccd4e0,

    // used in LOG
    $log-message:          #0CCA5D,
    $log-info:             #5E3673,
    $log-warning:          #FCC937,
    $log-error:            #FD354B,

    $logo-background: rgba(0,0,0,0.75),

    $color-adjust-sign: -1,
);

@use '../../mol-plugin-ui/skin/base/base';
@use '../../mol-plugin-ui/skin/base/vars' as *;

a {
    color: $font-color;
    &:hover {
        color: $hover-font-color;
    }
}


.msp-snapshot-description-me {
    background: color.change($default-background, $alpha: 0.5, $space: rgb);

    position: absolute;
    height: 50vh; // 50% of the viewport height
    left: 0;
    top: $control-spacing + $row-height;
    padding: (0.66 * $control-spacing) $control-spacing;

    resize: both; /* Allows resizing in both directions */
    overflow: auto; /* Adjust as needed */

    a {
        text-decoration: underline;
        cursor: pointer;
        color: $font-color;
    }

    ul, ol {
        padding-left: $control-spacing + 4px;
    }

    &.hidden {
        display: none;
    }

    &.shown {
        display: block; // or 'flex', 'grid', etc. depending on your layout
    }
}
