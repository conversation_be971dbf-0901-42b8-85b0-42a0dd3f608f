import { Fragment as _Fragment, jsx as _jsx } from "react/jsx-runtime";
/**
 * Copyright (c) 2025 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
import { MolViewSpec } from '../../../extensions/mvs/behavior';
import { loadMVSData } from '../../../extensions/mvs/components/formats';
import { PluginComponent } from '../../../mol-plugin-state/component';
import { createPluginUI } from '../../../mol-plugin-ui';
import { renderReact18 } from '../../../mol-plugin-ui/react18';
import { DefaultPluginUISpec } from '../../../mol-plugin-ui/spec';
import { PluginCommands } from '../../../mol-plugin/commands';
import { PluginConfig } from '../../../mol-plugin/config';
import { PluginSpec } from '../../../mol-plugin/spec';
import { getMVSStoriesContext } from '../context';
export class MVSStoriesViewerModel extends PluginComponent {
    async mount(root) {
        var _a;
        const spec = DefaultPluginUISpec();
        this.plugin = await createPluginUI({
            target: root,
            render: renderReact18,
            spec: {
                ...spec,
                layout: {
                    initial: {
                        isExpanded: false,
                        showControls: false,
                        controlsDisplay: 'landscape',
                    },
                },
                components: {
                    remoteState: 'none',
                    viewport: {
                        snapshotDescription: EmptyDescription,
                    }
                },
                behaviors: [
                    ...spec.behaviors,
                    PluginSpec.Behavior(MolViewSpec)
                ],
                config: [
                    [PluginConfig.Viewport.ShowAnimation, false],
                ]
            }
        });
        this.subscribe(this.context.commands, async (cmd) => {
            var _a, _b;
            if (!cmd || !this.plugin)
                return;
            try {
                this.context.state.isLoading.next(true);
                if (cmd.kind === 'load-mvs') {
                    if (cmd.url) {
                        const data = await this.plugin.runTask(this.plugin.fetch({ url: cmd.url, type: cmd.format === 'mvsx' ? 'binary' : 'string' }));
                        await loadMVSData(this.plugin, data, (_a = cmd.format) !== null && _a !== void 0 ? _a : 'mvsj', { sourceUrl: cmd.url });
                    }
                    else if (cmd.data) {
                        await loadMVSData(this.plugin, cmd.data, (_b = cmd.format) !== null && _b !== void 0 ? _b : 'mvsj');
                    }
                }
            }
            catch (e) {
                console.error(e);
                PluginCommands.Toast.Show(this.plugin, { key: '<mvsload>', title: 'Error', message: (e === null || e === void 0 ? void 0 : e.message) ? `${e === null || e === void 0 ? void 0 : e.message}` : `${e}`, timeoutMs: 10000 });
            }
            finally {
                this.context.state.isLoading.next(false);
            }
        });
        const viewers = this.context.state.viewers.value;
        const next = [...viewers, { name: (_a = this.options) === null || _a === void 0 ? void 0 : _a.name, model: this }];
        this.context.state.viewers.next(next);
    }
    constructor(options) {
        super();
        this.options = options;
        this.plugin = undefined;
        this.context = getMVSStoriesContext(options === null || options === void 0 ? void 0 : options.context);
        const viewers = this.context.state.viewers.value;
        const index = viewers.findIndex(v => v.name === (options === null || options === void 0 ? void 0 : options.name));
        if (index >= 0) {
            const next = [...viewers];
            next[index].model.dispose();
            next.splice(index, 0);
            this.context.state.viewers.next(next);
        }
    }
}
function EmptyDescription() {
    return _jsx(_Fragment, {});
}
export class MVSStoriesViewer extends HTMLElement {
    async connectedCallback() {
        var _a, _b;
        this.model = new MVSStoriesViewerModel({
            name: (_a = this.getAttribute('name')) !== null && _a !== void 0 ? _a : undefined,
            context: { name: (_b = this.getAttribute('context-name')) !== null && _b !== void 0 ? _b : undefined },
        });
        await this.model.mount(this);
    }
    disconnectedCallback() {
        var _a;
        (_a = this.model) === null || _a === void 0 ? void 0 : _a.dispose();
        this.model = undefined;
    }
    constructor() {
        super();
        this.model = undefined;
    }
}
window.customElements.define('mvs-stories-viewer', MVSStoriesViewer);
