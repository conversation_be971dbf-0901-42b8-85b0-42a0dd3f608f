#!/usr/bin/env node
/**
 * Copyright (c) 2017-2019 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 */
import * as argparse from 'argparse';
import * as util from 'util';
import * as fs from 'fs';
import * as zlib from 'zlib';
import { convert } from './converter';
require('util.promisify').shim();
async function process(srcPath, outPath, configPath, filterPath) {
    const config = configPath ? JSON.parse(fs.readFileSync(configPath, 'utf8')) : void 0;
    const filter = filterPath ? fs.readFileSync(filterPath, 'utf8') : void 0;
    const res = await convert(srcPath, srcPath.toLowerCase().indexOf('.bcif') > 0, config, filter);
    await write(outPath, res);
}
const zipAsync = util.promisify(zlib.gzip);
async function write(outPath, res) {
    const isGz = /\.gz$/i.test(outPath);
    if (isGz) {
        res = await zipAsync(res);
    }
    fs.writeFileSync(outPath, res);
}
function run(args) {
    process(args.src, args.out, args.config, args.filter);
}
const parser = new argparse.ArgumentParser({
    add_help: true,
    description: 'Convert any BCIF file to a CIF file or vice versa'
});
parser.add_argument('src', {
    help: 'Source file path'
});
parser.add_argument('out', {
    help: 'Output file path'
});
parser.add_argument('-c', '--config', {
    help: 'Optional encoding strategy/precision config path',
    required: false
});
parser.add_argument('-f', '--filter', {
    help: 'Optional filter whitelist/blacklist path',
    required: false
});
const args = parser.parse_args();
if (args) {
    run(args);
}
