#!/usr/bin/env node
/**
 * Copyright (c) 2023-2024 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 *
 * Command-line application for rendering images from MolViewSpec files
 * From Molstar NPM package:
 *     npm install molstar canvas gl jpeg-js pngjs
 *     npx mvs-render -i examples/mvs/1cbs.mvsj -o ../outputs/1cbs.png --size 800x600 --molj
 * From Molstar source code:
 *     npm install
 *     npm install --no-save canvas gl jpeg-js pngjs  // these packages are not listed in Mol* dependencies for performance reasons
 *     npm run build
 *     node lib/commonjs/cli/mvs/mvs-render -i examples/mvs/1cbs.mvsj -o ../outputs/1cbs.png --size 800x600 --molj
 */
export {};
