"use strict";
/**
 * Copyright (c) 2022-2024 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.MesoscaleExplorer = exports.consoleStats = exports.setTimingMode = exports.setProductionMode = exports.setDebugMode = exports.version = void 0;
const mp4_export_1 = require("../../extensions/mp4-export");
const mol_plugin_ui_1 = require("../../mol-plugin-ui");
const react18_1 = require("../../mol-plugin-ui/react18");
const spec_1 = require("../../mol-plugin-ui/spec");
const config_1 = require("../../mol-plugin/config");
const spec_2 = require("../../mol-plugin/spec");
require("../../mol-util/polyfill");
const type_helpers_1 = require("../../mol-util/type-helpers");
const backgrounds_1 = require("../../extensions/backgrounds");
const panels_1 = require("./ui/panels");
const color_1 = require("../../mol-util/color");
const spacefill_1 = require("../../mol-repr/structure/representation/spacefill");
const behavior_1 = require("../../mol-plugin/behavior");
const camera_1 = require("./behavior/camera");
const state_1 = require("./data/state");
const select_1 = require("./behavior/select");
const states_1 = require("./ui/states");
const assets_1 = require("../../mol-util/assets");
const camera_spin_1 = require("../../mol-plugin-state/animation/built-in/camera-spin");
const camera_rock_1 = require("../../mol-plugin-state/animation/built-in/camera-rock");
const state_snapshots_1 = require("../../mol-plugin-state/animation/built-in/state-snapshots");
const entities_1 = require("./ui/entities");
var version_1 = require("../../mol-plugin/version");
Object.defineProperty(exports, "version", { enumerable: true, get: function () { return version_1.PLUGIN_VERSION; } });
var debug_1 = require("../../mol-util/debug");
Object.defineProperty(exports, "setDebugMode", { enumerable: true, get: function () { return debug_1.setDebugMode; } });
Object.defineProperty(exports, "setProductionMode", { enumerable: true, get: function () { return debug_1.setProductionMode; } });
Object.defineProperty(exports, "setTimingMode", { enumerable: true, get: function () { return debug_1.setTimingMode; } });
Object.defineProperty(exports, "consoleStats", { enumerable: true, get: function () { return debug_1.consoleStats; } });
//
const Extensions = {
    'backgrounds': spec_2.PluginSpec.Behavior(backgrounds_1.Backgrounds),
    'mp4-export': spec_2.PluginSpec.Behavior(mp4_export_1.Mp4Export),
};
const DefaultMesoscaleExplorerOptions = {
    customFormats: [],
    extensions: (0, type_helpers_1.ObjectKeys)(Extensions),
    layoutIsExpanded: true,
    layoutShowControls: true,
    layoutShowRemoteState: true,
    layoutControlsDisplay: 'reactive',
    layoutShowSequence: true,
    layoutShowLog: true,
    layoutShowLeftPanel: true,
    collapseLeftPanel: false,
    collapseRightPanel: false,
    disableAntialiasing: config_1.PluginConfig.General.DisableAntialiasing.defaultValue,
    pixelScale: config_1.PluginConfig.General.PixelScale.defaultValue,
    pickScale: config_1.PluginConfig.General.PickScale.defaultValue,
    transparency: 'blended',
    preferWebgl1: config_1.PluginConfig.General.PreferWebGl1.defaultValue,
    allowMajorPerformanceCaveat: config_1.PluginConfig.General.AllowMajorPerformanceCaveat.defaultValue,
    powerPreference: config_1.PluginConfig.General.PowerPreference.defaultValue,
    resolutionMode: config_1.PluginConfig.General.ResolutionMode.defaultValue,
    illumination: false,
    viewportShowExpand: config_1.PluginConfig.Viewport.ShowExpand.defaultValue,
    viewportShowControls: config_1.PluginConfig.Viewport.ShowControls.defaultValue,
    viewportShowSettings: config_1.PluginConfig.Viewport.ShowSettings.defaultValue,
    viewportShowSelectionMode: true,
    viewportShowAnimation: false,
    viewportShowTrajectoryControls: false,
    pluginStateServer: config_1.PluginConfig.State.DefaultServer.defaultValue,
    volumeStreamingServer: config_1.PluginConfig.VolumeStreaming.DefaultServer.defaultValue,
    volumeStreamingDisabled: !config_1.PluginConfig.VolumeStreaming.Enabled.defaultValue,
    pdbProvider: config_1.PluginConfig.Download.DefaultPdbProvider.defaultValue,
    emdbProvider: config_1.PluginConfig.Download.DefaultEmdbProvider.defaultValue,
    saccharideCompIdMapType: 'default',
    graphicsMode: 'quality',
    driver: undefined
};
class MesoscaleExplorer {
    constructor(plugin) {
        this.plugin = plugin;
    }
    async loadExample(id) {
        const entries = this.plugin.customState.examples || [];
        const entry = entries.find(e => e.id === id);
        if (entry !== undefined) {
            await (0, states_1.loadExampleEntry)(this.plugin, entry);
        }
    }
    async loadUrl(url, type) {
        await (0, states_1.loadUrl)(this.plugin, url, type);
    }
    async loadPdb(id) {
        await (0, states_1.loadPdb)(this.plugin, id);
    }
    /**
     * @deprecated Scheduled for removal in v5. Use {@link loadPdbIhm | loadPdbIhm(id: string)} instead.
     */
    async loadPdbDev(id) {
        await this.loadPdbIhm(id);
    }
    async loadPdbIhm(id) {
        await (0, states_1.loadPdbIhm)(this.plugin, id);
    }
    static async create(elementOrId, options = {}) {
        var _a, _b;
        const definedOptions = {};
        // filter for defined properies only so the default values
        // are property applied
        for (const p of Object.keys(options)) {
            if (options[p] !== void 0)
                definedOptions[p] = options[p];
        }
        const o = { ...DefaultMesoscaleExplorerOptions, ...definedOptions };
        const defaultSpec = (0, spec_1.DefaultPluginUISpec)();
        const spec = {
            actions: defaultSpec.actions,
            behaviors: [
                spec_2.PluginSpec.Behavior(behavior_1.PluginBehaviors.Camera.CameraAxisHelper),
                spec_2.PluginSpec.Behavior(behavior_1.PluginBehaviors.Camera.CameraControls),
                spec_2.PluginSpec.Behavior(camera_1.MesoFocusLoci),
                spec_2.PluginSpec.Behavior(select_1.MesoSelectLoci),
                spec_2.PluginSpec.Behavior(behavior_1.PluginBehaviors.Representation.SelectLoci),
                ...o.extensions.map(e => Extensions[e]),
            ],
            animations: [
                camera_spin_1.AnimateCameraSpin,
                camera_rock_1.AnimateCameraRock,
                state_snapshots_1.AnimateStateSnapshots,
            ],
            customParamEditors: defaultSpec.customParamEditors,
            customFormats: o === null || o === void 0 ? void 0 : o.customFormats,
            layout: {
                initial: {
                    isExpanded: o.layoutIsExpanded,
                    showControls: o.layoutShowControls,
                    controlsDisplay: o.layoutControlsDisplay,
                    regionState: {
                        bottom: 'full',
                        left: o.collapseLeftPanel ? 'collapsed' : 'full',
                        right: o.collapseRightPanel ? 'hidden' : 'full',
                        top: 'full',
                    }
                },
            },
            components: {
                ...defaultSpec.components,
                controls: {
                    ...(_a = defaultSpec.components) === null || _a === void 0 ? void 0 : _a.controls,
                    top: 'none',
                    bottom: 'none',
                    left: panels_1.LeftPanel,
                    right: panels_1.RightPanel,
                },
                remoteState: 'none',
                viewport: {
                    snapshotDescription: entities_1.MesoViewportSnapshotDescription,
                }
            },
            config: [
                [config_1.PluginConfig.General.DisableAntialiasing, o.disableAntialiasing],
                [config_1.PluginConfig.General.PixelScale, o.pixelScale],
                [config_1.PluginConfig.General.PickScale, o.pickScale],
                [config_1.PluginConfig.General.Transparency, o.transparency],
                [config_1.PluginConfig.General.PreferWebGl1, o.preferWebgl1],
                [config_1.PluginConfig.General.AllowMajorPerformanceCaveat, o.allowMajorPerformanceCaveat],
                [config_1.PluginConfig.General.PowerPreference, o.powerPreference],
                [config_1.PluginConfig.General.ResolutionMode, o.resolutionMode],
                [config_1.PluginConfig.Viewport.ShowExpand, o.viewportShowExpand],
                [config_1.PluginConfig.Viewport.ShowControls, o.viewportShowControls],
                [config_1.PluginConfig.Viewport.ShowSettings, o.viewportShowSettings],
                [config_1.PluginConfig.Viewport.ShowSelectionMode, o.viewportShowSelectionMode],
                [config_1.PluginConfig.Viewport.ShowAnimation, o.viewportShowAnimation],
                [config_1.PluginConfig.Viewport.ShowTrajectoryControls, o.viewportShowTrajectoryControls],
                [config_1.PluginConfig.State.DefaultServer, o.pluginStateServer],
                [config_1.PluginConfig.State.CurrentServer, o.pluginStateServer],
                [config_1.PluginConfig.VolumeStreaming.DefaultServer, o.volumeStreamingServer],
                [config_1.PluginConfig.VolumeStreaming.Enabled, !o.volumeStreamingDisabled],
                [config_1.PluginConfig.Download.DefaultPdbProvider, o.pdbProvider],
                [config_1.PluginConfig.Download.DefaultEmdbProvider, o.emdbProvider],
                [config_1.PluginConfig.Structure.SaccharideCompIdMapType, o.saccharideCompIdMapType],
            ]
        };
        const element = typeof elementOrId === 'string'
            ? document.getElementById(elementOrId)
            : elementOrId;
        if (!element)
            throw new Error(`Could not get element with id '${elementOrId}'`);
        const plugin = await (0, mol_plugin_ui_1.createPluginUI)({
            target: element,
            spec,
            render: react18_1.renderReact18,
            onBeforeUIRender: async (plugin) => {
                let examples = undefined;
                try {
                    examples = await plugin.fetch({ url: '../examples/list.json', type: 'json' }).run();
                    // extend the array with file tour.json if it exists
                    const tour = await plugin.fetch({ url: '../examples/tour.json', type: 'json' }).run();
                    if (tour) {
                        examples = examples === null || examples === void 0 ? void 0 : examples.concat(tour);
                    }
                }
                catch (e) {
                    console.log(e);
                }
                plugin.customState = {
                    examples,
                    graphicsMode: o.graphicsMode,
                    illumination: o.illumination,
                    driver: o.driver,
                    stateCache: {},
                };
                await state_1.MesoscaleState.init(plugin);
            }
        });
        (_b = plugin.canvas3d) === null || _b === void 0 ? void 0 : _b.setProps({
            renderer: {
                backgroundColor: (0, color_1.Color)(0x101010),
            },
            cameraFog: { name: 'off', params: {} },
            hiZ: { enabled: true },
        });
        plugin.representation.structure.registry.clear();
        plugin.representation.structure.registry.add(spacefill_1.SpacefillRepresentationProvider);
        plugin.state.setSnapshotParams({
            image: true,
            componentManager: false,
            structureSelection: true,
            behavior: true,
        });
        plugin.managers.lociLabels.clearProviders();
        plugin.managers.dragAndDrop.addHandler('mesoscale-explorer', (files) => {
            const sessions = files.filter(f => {
                const fn = f.name.toLowerCase();
                return fn.endsWith('.molx') || fn.endsWith('.molj');
            });
            if (sessions.length > 0) {
                (0, states_1.openState)(plugin, sessions[0]);
            }
            else {
                plugin.runTask(plugin.state.data.applyAction(states_1.LoadModel, {
                    files: files.map(f => assets_1.Asset.File(f)),
                }));
            }
            return true;
        });
        plugin.state.events.object.created.subscribe(e => {
            plugin.customState.stateCache = {};
        });
        plugin.state.events.object.removed.subscribe(e => {
            plugin.customState.stateCache = {};
        });
        return new MesoscaleExplorer(plugin);
    }
    handleResize() {
        this.plugin.layout.events.updated.next(void 0);
    }
    dispose() {
        this.plugin.dispose();
    }
}
exports.MesoscaleExplorer = MesoscaleExplorer;
