/**
 * Copyright (c) 2025 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
import { BehaviorSubject } from 'rxjs';
import { MVSData } from '../../extensions/mvs/mvs-data';
import type { MVSStoriesViewerModel } from './elements/viewer';
export type MVSStoriesCommand = {
    kind: 'load-mvs';
    format?: 'mvsj' | 'mvsx';
    url?: string;
    data?: MVSData | string | Uint8Array;
};
export declare class MVSStoriesContext {
    name?: string | undefined;
    commands: BehaviorSubject<{
        kind: "load-mvs";
        format?: "mvsj" | "mvsx";
        url?: string;
        data?: MVSData | string | Uint8Array;
    } | undefined>;
    state: {
        viewers: BehaviorSubject<{
            name?: string;
            model: MVSStoriesViewerModel;
        }[]>;
        isLoading: BehaviorSubject<boolean>;
    };
    dispatch(command: MVSStoriesCommand): void;
    constructor(name?: string | undefined);
}
export declare function getMVSStoriesContext(options?: {
    name?: string;
    container?: object;
}): any;
