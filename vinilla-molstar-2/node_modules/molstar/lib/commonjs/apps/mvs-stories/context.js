"use strict";
/**
 * Copyright (c) 2025 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.MVSStoriesContext = void 0;
exports.getMVSStoriesContext = getMVSStoriesContext;
const rxjs_1 = require("rxjs");
class MVSStoriesContext {
    dispatch(command) {
        this.commands.next(command);
    }
    constructor(name) {
        this.name = name;
        this.commands = new rxjs_1.BehaviorSubject(undefined);
        this.state = {
            viewers: new rxjs_1.BehaviorSubject([]),
            isLoading: new rxjs_1.BehaviorSubject(false),
        };
    }
}
exports.MVSStoriesContext = MVSStoriesContext;
function getMVSStoriesContext(options) {
    var _a, _b, _c;
    const container = (_a = options === null || options === void 0 ? void 0 : options.container) !== null && _a !== void 0 ? _a : window;
    (_b = container.componentContexts) !== null && _b !== void 0 ? _b : (container.componentContexts = {});
    const name = (_c = options === null || options === void 0 ? void 0 : options.name) !== null && _c !== void 0 ? _c : '<default>';
    if (!container.componentContexts[name]) {
        container.componentContexts[name] = new MVSStoriesContext(options === null || options === void 0 ? void 0 : options.name);
    }
    return container.componentContexts[name];
}
