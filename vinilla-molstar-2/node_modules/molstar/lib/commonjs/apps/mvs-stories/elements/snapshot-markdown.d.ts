/**
 * Copyright (c) 2025 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
import { BehaviorSubject } from 'rxjs';
import { PluginComponent } from '../../../mol-plugin-state/component';
import { MVSStoriesContext } from '../context';
import { MVSStoriesViewerModel } from './viewer';
import { PluginStateSnapshotManager } from '../../../mol-plugin-state/manager/snapshots';
export declare class MVSStoriesSnapshotMarkdownModel extends PluginComponent {
    private options?;
    readonly context: MVSStoriesContext;
    root: HTMLElement | undefined;
    state: BehaviorSubject<{
        entry?: PluginStateSnapshotManager.Entry;
        index?: number;
        all: PluginStateSnapshotManager.Entry[];
    }>;
    get viewer(): {
        name?: string;
        model: MVSStoriesViewerModel;
    } | undefined;
    sync(): void;
    mount(root: HTMLElement): Promise<void>;
    constructor(options?: {
        context?: {
            name?: string;
            container?: object;
        };
        viewerName?: string;
    } | undefined);
}
export declare function MVSStoriesSnapshotMarkdownUI({ model }: {
    model: MVSStoriesSnapshotMarkdownModel;
}): import("react/jsx-runtime").JSX.Element;
export declare class MVSStoriesSnapshotMarkdownViewer extends HTMLElement {
    private model;
    connectedCallback(): Promise<void>;
    disconnectedCallback(): void;
    constructor();
}
