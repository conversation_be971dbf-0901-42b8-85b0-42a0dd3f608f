/**
 * Copyright (c) 2025 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
import { PluginComponent } from '../../../mol-plugin-state/component';
import { PluginContext } from '../../../mol-plugin/context';
import { MVSStoriesContext } from '../context';
export declare class MVSStoriesViewerModel extends PluginComponent {
    private options?;
    readonly context: MVSStoriesContext;
    plugin?: PluginContext;
    mount(root: HTMLElement): Promise<void>;
    constructor(options?: {
        context?: {
            name?: string;
            container?: object;
        };
        name?: string;
    } | undefined);
}
export declare class MVSStoriesViewer extends HTMLElement {
    private model;
    connectedCallback(): Promise<void>;
    disconnectedCallback(): void;
    constructor();
}
