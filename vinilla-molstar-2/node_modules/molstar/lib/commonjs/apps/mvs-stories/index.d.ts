/**
 * Copyright (c) 2025 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
import './elements';
import { MVSData } from '../../extensions/mvs/mvs-data';
import './favicon.ico';
import '../../mol-plugin-ui/skin/light.scss';
import './styles.scss';
import './index.html';
export declare function getContext(name?: string): any;
export declare function loadFromURL(url: string, options?: {
    format: 'mvsx' | 'mvsj';
    contextName?: string;
}): void;
export declare function loadFromData(data: MVSData | string | Uint8Array, options?: {
    format: 'mvsx' | 'mvsj';
    contextName?: string;
}): void;
export { MVSData };
