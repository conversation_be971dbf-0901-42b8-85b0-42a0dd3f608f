"use strict";
/**
 * Copyright (c) 2025 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.MVSData = void 0;
exports.getContext = getContext;
exports.loadFromURL = loadFromURL;
exports.loadFromData = loadFromData;
const context_1 = require("./context");
require("./elements");
const mvs_data_1 = require("../../extensions/mvs/mvs-data");
Object.defineProperty(exports, "MVSData", { enumerable: true, get: function () { return mvs_data_1.MVSData; } });
require("./favicon.ico");
require("../../mol-plugin-ui/skin/light.scss");
require("./styles.scss");
require("./index.html");
function getContext(name) {
    return (0, context_1.getMVSStoriesContext)({ name });
}
function loadFromURL(url, options) {
    setTimeout(() => {
        var _a;
        getContext(options === null || options === void 0 ? void 0 : options.contextName).dispatch({
            kind: 'load-mvs',
            format: (_a = options === null || options === void 0 ? void 0 : options.format) !== null && _a !== void 0 ? _a : 'mvsj',
            url,
        });
    }, 0);
}
function loadFromData(data, options) {
    setTimeout(() => {
        var _a;
        getContext(options === null || options === void 0 ? void 0 : options.contextName).dispatch({
            kind: 'load-mvs',
            format: (_a = options === null || options === void 0 ? void 0 : options.format) !== null && _a !== void 0 ? _a : 'mvsj',
            data,
        });
    }, 0);
}
