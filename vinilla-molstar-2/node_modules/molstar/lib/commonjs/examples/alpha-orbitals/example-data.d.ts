import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '../../extensions/alpha-orbitals/data-model';
import { SphericalBasis<PERSON>rde<PERSON> } from '../../extensions/alpha-orbitals/spherical-functions';
export declare const DemoMoleculeSDF = "60823\n  -OEChem-10232017443D\n\n 76 79  0     1  0  0  0  0  0999 V2000\n    4.2325    4.8495   -0.2414 F   0  0  0  0  0  0  0  0  0  0  0  0\n    3.3901   -2.4373    3.0602 O   0  0  0  0  0  0  0  0  0  0  0  0\n    5.8234   -0.9362   -0.1694 O   0  0  0  0  0  0  0  0  0  0  0  0\n   -4.0484   -0.0236    1.5867 O   0  0  0  0  0  0  0  0  0  0  0  0\n    5.9513   -3.8220   -3.4785 O   0  0  0  0  0  0  0  0  0  0  0  0\n    5.7344   -1.5960   -3.0713 O   0  0  0  0  0  0  0  0  0  0  0  0\n    0.3212    0.0758    1.5166 N   0  0  0  0  0  0  0  0  0  0  0  0\n   -3.4511   -1.1575   -0.3628 N   0  0  0  0  0  0  0  0  0  0  0  0\n   -0.8535   -0.6250    1.5997 C   0  0  0  0  0  0  0  0  0  0  0  0\n    1.5803   -0.3344    2.1283 C   0  0  0  0  0  0  0  0  0  0  0  0\n    0.1278    1.2100    0.7719 C   0  0  0  0  0  0  0  0  0  0  0  0\n   -0.9533   -1.9004    2.3428 C   0  0  0  0  0  0  0  0  0  0  0  0\n    2.4473   -1.2165    1.2192 C   0  0  0  0  0  0  0  0  0  0  0  0\n   -1.8115    0.0728    0.8978 C   0  0  0  0  0  0  0  0  0  0  0  0\n   -1.1907    1.2338    0.3742 C   0  0  0  0  0  0  0  0  0  0  0  0\n    3.7416   -1.7120    1.8781 C   0  0  2  0  0  0  0  0  0  0  0  0\n    4.5791   -2.6487    0.9881 C   0  0  0  0  0  0  0  0  0  0  0  0\n    1.1949    2.1553    0.5088 C   0  0  0  0  0  0  0  0  0  0  0  0\n   -1.3241   -3.0999    1.4405 C   0  0  0  0  0  0  0  0  0  0  0  0\n   -1.9352   -1.8320    3.5352 C   0  0  0  0  0  0  0  0  0  0  0  0\n   -1.7591    2.2833   -0.4268 C   0  0  0  0  0  0  0  0  0  0  0  0\n    5.0315   -2.1044   -0.3741 C   0  0  2  0  0  0  0  0  0  0  0  0\n   -3.1792   -0.3427    0.7529 C   0  0  0  0  0  0  0  0  0  0  0  0\n    5.8126   -3.1249   -1.2008 C   0  0  0  0  0  0  0  0  0  0  0  0\n    1.9989    2.0028   -0.6208 C   0  0  0  0  0  0  0  0  0  0  0  0\n    1.4170    3.2178    1.3848 C   0  0  0  0  0  0  0  0  0  0  0  0\n   -2.3259    3.4003    0.1871 C   0  0  0  0  0  0  0  0  0  0  0  0\n   -1.7439    2.1847   -1.8182 C   0  0  0  0  0  0  0  0  0  0  0  0\n    3.0251    2.9131   -0.8744 C   0  0  0  0  0  0  0  0  0  0  0  0\n    2.4429    4.1280    1.1313 C   0  0  0  0  0  0  0  0  0  0  0  0\n   -2.8777    4.4186   -0.5902 C   0  0  0  0  0  0  0  0  0  0  0  0\n   -2.2957    3.2032   -2.5955 C   0  0  0  0  0  0  0  0  0  0  0  0\n    3.2469    3.9758    0.0018 C   0  0  0  0  0  0  0  0  0  0  0  0\n   -4.6784   -1.7492   -0.7544 C   0  0  0  0  0  0  0  0  0  0  0  0\n    5.8309   -2.7456   -2.6619 C   0  0  0  0  0  0  0  0  0  0  0  0\n   -2.8626    4.3200   -1.9814 C   0  0  0  0  0  0  0  0  0  0  0  0\n   -5.8255   -1.5489    0.0136 C   0  0  0  0  0  0  0  0  0  0  0  0\n   -4.7392   -2.5312   -1.9079 C   0  0  0  0  0  0  0  0  0  0  0  0\n   -7.0333   -2.1307   -0.3717 C   0  0  0  0  0  0  0  0  0  0  0  0\n   -5.9471   -3.1129   -2.2932 C   0  0  0  0  0  0  0  0  0  0  0  0\n   -7.0940   -2.9125   -1.5250 C   0  0  0  0  0  0  0  0  0  0  0  0\n    2.1382    0.5731    2.3855 H   0  0  0  0  0  0  0  0  0  0  0  0\n    1.3708   -0.8002    3.0947 H   0  0  0  0  0  0  0  0  0  0  0  0\n    0.0077   -2.1843    2.7778 H   0  0  0  0  0  0  0  0  0  0  0  0\n    1.8521   -2.0869    0.9138 H   0  0  0  0  0  0  0  0  0  0  0  0\n    2.6842   -0.6643    0.3050 H   0  0  0  0  0  0  0  0  0  0  0  0\n    4.3434   -0.8508    2.1898 H   0  0  0  0  0  0  0  0  0  0  0  0\n    5.4691   -2.9413    1.5624 H   0  0  0  0  0  0  0  0  0  0  0  0\n    4.0142   -3.5780    0.8346 H   0  0  0  0  0  0  0  0  0  0  0  0\n   -0.6944   -3.1286    0.5448 H   0  0  0  0  0  0  0  0  0  0  0  0\n   -1.1783   -4.0399    1.9847 H   0  0  0  0  0  0  0  0  0  0  0  0\n   -2.3703   -3.0713    1.1264 H   0  0  0  0  0  0  0  0  0  0  0  0\n   -1.8631   -2.7437    4.1386 H   0  0  0  0  0  0  0  0  0  0  0  0\n   -1.7020   -0.9798    4.1827 H   0  0  0  0  0  0  0  0  0  0  0  0\n   -2.9745   -1.7380    3.2088 H   0  0  0  0  0  0  0  0  0  0  0  0\n    4.1475   -1.8000   -0.9464 H   0  0  0  0  0  0  0  0  0  0  0  0\n    6.8506   -3.2027   -0.8609 H   0  0  0  0  0  0  0  0  0  0  0  0\n    5.3309   -4.1061   -1.1198 H   0  0  0  0  0  0  0  0  0  0  0  0\n    1.8373    1.1817   -1.3152 H   0  0  0  0  0  0  0  0  0  0  0  0\n    0.7989    3.3503    2.2697 H   0  0  0  0  0  0  0  0  0  0  0  0\n    4.2168   -2.6972    3.5013 H   0  0  0  0  0  0  0  0  0  0  0  0\n   -2.3461    3.4925    1.2706 H   0  0  0  0  0  0  0  0  0  0  0  0\n   -1.3066    1.3204   -2.3125 H   0  0  0  0  0  0  0  0  0  0  0  0\n   -2.6662   -1.3612   -0.9788 H   0  0  0  0  0  0  0  0  0  0  0  0\n    3.6513    2.7948   -1.7537 H   0  0  0  0  0  0  0  0  0  0  0  0\n    2.6161    4.9554    1.8131 H   0  0  0  0  0  0  0  0  0  0  0  0\n    5.2440   -0.2436    0.1902 H   0  0  0  0  0  0  0  0  0  0  0  0\n   -3.3191    5.2883   -0.1123 H   0  0  0  0  0  0  0  0  0  0  0  0\n   -2.2842    3.1265   -3.6788 H   0  0  0  0  0  0  0  0  0  0  0  0\n   -3.2921    5.1128   -2.5867 H   0  0  0  0  0  0  0  0  0  0  0  0\n   -5.8624   -0.9592    0.9179 H   0  0  0  0  0  0  0  0  0  0  0  0\n   -3.8528   -2.6952   -2.5153 H   0  0  0  0  0  0  0  0  0  0  0  0\n    5.9549   -3.5647   -4.4251 H   0  0  0  0  0  0  0  0  0  0  0  0\n   -7.9272   -1.9757    0.2256 H   0  0  0  0  0  0  0  0  0  0  0  0\n   -5.9946   -3.7218   -3.1913 H   0  0  0  0  0  0  0  0  0  0  0  0\n   -8.0344   -3.3656   -1.8250 H   0  0  0  0  0  0  0  0  0  0  0  0\n  1 33  1  0  0  0  0\n  2 16  1  0  0  0  0\n  2 61  1  0  0  0  0\n  3 22  1  0  0  0  0\n  3 67  1  0  0  0  0\n  4 23  2  0  0  0  0\n  5 35  1  0  0  0  0\n  5 73  1  0  0  0  0\n  6 35  2  0  0  0  0\n  7  9  1  0  0  0  0\n  7 10  1  0  0  0  0\n  7 11  1  0  0  0  0\n  8 23  1  0  0  0  0\n  8 34  1  0  0  0  0\n  8 64  1  0  0  0  0\n  9 12  1  0  0  0  0\n  9 14  2  0  0  0  0\n 10 13  1  0  0  0  0\n 10 42  1  0  0  0  0\n 10 43  1  0  0  0  0\n 11 15  2  0  0  0  0\n 11 18  1  0  0  0  0\n 12 19  1  0  0  0  0\n 12 20  1  0  0  0  0\n 12 44  1  0  0  0  0\n 13 16  1  0  0  0  0\n 13 45  1  0  0  0  0\n 13 46  1  0  0  0  0\n 14 15  1  0  0  0  0\n 14 23  1  0  0  0  0\n 15 21  1  0  0  0  0\n 16 17  1  0  0  0  0\n 16 47  1  0  0  0  0\n 17 22  1  0  0  0  0\n 17 48  1  0  0  0  0\n 17 49  1  0  0  0  0\n 18 25  2  0  0  0  0\n 18 26  1  0  0  0  0\n 19 50  1  0  0  0  0\n 19 51  1  0  0  0  0\n 19 52  1  0  0  0  0\n 20 53  1  0  0  0  0\n 20 54  1  0  0  0  0\n 20 55  1  0  0  0  0\n 21 27  2  0  0  0  0\n 21 28  1  0  0  0  0\n 22 24  1  0  0  0  0\n 22 56  1  0  0  0  0\n 24 35  1  0  0  0  0\n 24 57  1  0  0  0  0\n 24 58  1  0  0  0  0\n 25 29  1  0  0  0  0\n 25 59  1  0  0  0  0\n 26 30  2  0  0  0  0\n 26 60  1  0  0  0  0\n 27 31  1  0  0  0  0\n 27 62  1  0  0  0  0\n 28 32  2  0  0  0  0\n 28 63  1  0  0  0  0\n 29 33  2  0  0  0  0\n 29 65  1  0  0  0  0\n 30 33  1  0  0  0  0\n 30 66  1  0  0  0  0\n 31 36  2  0  0  0  0\n 31 68  1  0  0  0  0\n 32 36  1  0  0  0  0\n 32 69  1  0  0  0  0\n 34 37  2  0  0  0  0\n 34 38  1  0  0  0  0\n 36 70  1  0  0  0  0\n 37 39  1  0  0  0  0\n 37 71  1  0  0  0  0\n 38 40  2  0  0  0  0\n 38 72  1  0  0  0  0\n 39 41  2  0  0  0  0\n 39 74  1  0  0  0  0\n 40 41  1  0  0  0  0\n 40 75  1  0  0  0  0\n 41 76  1  0  0  0  0\nM  END\n";
export declare const DemoOrbitals: {
    order: SphericalBasisOrder;
    basis: Basis;
    orbitals: AlphaOrbital[];
};
