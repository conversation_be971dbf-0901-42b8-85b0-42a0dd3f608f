/**
 * Copyright (c) 2020-2022 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
import { BehaviorSubject } from 'rxjs';
import { AlphaOrbital, Basis } from '../../extensions/alpha-orbitals/data-model';
import { SphericalBasisOrder } from '../../extensions/alpha-orbitals/spherical-functions';
import { PluginUIContext } from '../../mol-plugin-ui/context';
import { ParamDefinition } from '../../mol-util/param-definition';
import './index.html';
import '../../mol-plugin-ui/skin/light.scss';
interface DemoInput {
    moleculeSdf: string;
    basis: Basis;
    order: SphericalBasisOrder;
    orbitals: AlphaOrbital[];
}
interface Params {
    show: {
        name: 'orbital';
        params: {
            index: number;
        };
    } | {
        name: 'density';
        params: {};
    };
    isoValue: number;
    gpuSurface: boolean;
}
export declare class AlphaOrbitalsExample {
    plugin: PluginUIContext;
    init(target: string | HTMLElement): Promise<void>;
    readonly params: BehaviorSubject<ParamDefinition.For<Params>>;
    readonly state: BehaviorSubject<Params>;
    private selectors?;
    private basis?;
    private currentParams;
    private clearVolume;
    private syncVolume;
    private setIsovalue;
    private volumeParams;
    load(input: DemoInput): Promise<void>;
}
export {};
