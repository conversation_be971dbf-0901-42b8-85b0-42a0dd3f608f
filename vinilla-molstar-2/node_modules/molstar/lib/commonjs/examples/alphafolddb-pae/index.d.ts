/**
 * Copyright (c) 2024 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
import { Viewer } from '../../apps/viewer/app';
import './index.html';
import '../../mol-plugin-ui/skin/light.scss';
export declare class AlphaFoldPAEExample {
    viewer: Viewer;
    plotContainerId: string;
    init(options: {
        pluginContainerId: string;
        plotContainerId: string;
    }): Promise<this>;
    load(afId: string): Promise<void>;
}
