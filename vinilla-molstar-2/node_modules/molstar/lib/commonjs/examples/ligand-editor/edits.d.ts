/**
 * Copyright (c) 2025 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
import { JSONCifLigandGraph, JSONCifLigandGraphAtom, JSONCifLigandGraphBondProps } from '../../extensions/json-cif/ligand-graph';
import { RGroupName } from './r-groups';
export declare const TopologyEdits: {
    setElement: (graph: JSONCifLigandGraph, atomIds: number[], type_symbol: string) => Promise<void>;
    addElement: (graph: JSONCifLigandGraph, parentId: number, type_symbol: string) => Promise<JSONCifLigandGraphAtom | undefined>;
    removeAtoms: (graph: JSONCifLigandGraph, atomIds: number[]) => Promise<void>;
    removeBonds: (graph: JSONCifLigandGraph, atomIds: number[]) => Promise<void>;
    updateBonds: (graph: JSONCifLigandGraph, atomIds: number[], props: JSONCifLigandGraphBondProps) => Promise<void>;
    attachRgroup: (graph: JSO<PERSON>ifLigandGraph, atomId: number, name: RGroupName) => Promise<void>;
};
export type GeometryEditFn = (param: number) => JSONCifLigandGraph;
export declare const GeometryEdits: {
    twist: (graph: JSONCifLigandGraph, atomIds: number[]) => GeometryEditFn;
    stretch: (graph: JSONCifLigandGraph, atomIds: number[]) => GeometryEditFn;
};
