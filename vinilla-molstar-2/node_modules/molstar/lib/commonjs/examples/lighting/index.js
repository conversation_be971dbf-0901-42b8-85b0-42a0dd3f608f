"use strict";
/**
 * Copyright (c) 2019-2024 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
Object.defineProperty(exports, "__esModule", { value: true });
const mol_plugin_ui_1 = require("../../mol-plugin-ui");
const react18_1 = require("../../mol-plugin-ui/react18");
const spec_1 = require("../../mol-plugin-ui/spec");
const commands_1 = require("../../mol-plugin/commands");
const assets_1 = require("../../mol-util/assets");
const color_1 = require("../../mol-util/color");
require("./index.html");
require("../../mol-plugin-ui/skin/light.scss");
const Canvas3DPresets = {
    illustrative: {
        canvas3d: {
            postprocessing: {
                occlusion: {
                    name: 'on',
                    params: {
                        samples: 32,
                        multiScale: { name: 'off', params: {} },
                        radius: 5,
                        bias: 0.8,
                        blurKernelSize: 15,
                        blurDepthBias: 0.5,
                        resolutionScale: 1,
                        color: (0, color_1.Color)(0x000000),
                        transparentThreshold: 0.4,
                    }
                },
                outline: {
                    name: 'on',
                    params: {
                        scale: 1,
                        threshold: 0.33,
                        color: (0, color_1.Color)(0x000000),
                        includeTransparent: true,
                    }
                },
                shadow: {
                    name: 'off',
                    params: {}
                },
            },
            renderer: {
                ambientIntensity: 1.0,
                light: []
            }
        }
    },
    occlusion: {
        canvas3d: {
            postprocessing: {
                occlusion: {
                    name: 'on',
                    params: {
                        samples: 32,
                        multiScale: { name: 'off', params: {} },
                        radius: 5,
                        bias: 0.8,
                        blurKernelSize: 15,
                        resolutionScale: 1,
                    }
                },
                outline: {
                    name: 'off',
                    params: {}
                },
                shadow: {
                    name: 'off',
                    params: {}
                },
            },
            renderer: {
                ambientIntensity: 0.4,
                light: [{ inclination: 180, azimuth: 0, color: color_1.Color.fromNormalizedRgb(1.0, 1.0, 1.0),
                        intensity: 0.6 }]
            }
        }
    },
    standard: {
        canvas3d: {
            postprocessing: {
                occlusion: { name: 'off', params: {} },
                outline: { name: 'off', params: {} },
                shadow: { name: 'off', params: {} },
            },
            renderer: {
                ambientIntensity: 0.4,
                light: [{ inclination: 180, azimuth: 0, color: color_1.Color.fromNormalizedRgb(1.0, 1.0, 1.0),
                        intensity: 0.6 }]
            }
        }
    }
};
class LightingDemo {
    constructor() {
        this.radius = 5;
        this.bias = 1.1;
        this.preset = 'illustrative';
    }
    async init(target) {
        this.plugin = await (0, mol_plugin_ui_1.createPluginUI)({
            target: typeof target === 'string' ? document.getElementById(target) : target,
            render: react18_1.renderReact18,
            spec: {
                ...(0, spec_1.DefaultPluginUISpec)(),
                layout: {
                    initial: {
                        isExpanded: false,
                        showControls: false
                    },
                },
                components: {
                    controls: { left: 'none', right: 'none', top: 'none', bottom: 'none' }
                }
            }
        });
        this.setPreset('illustrative');
    }
    setPreset(preset) {
        var _a;
        const props = Canvas3DPresets[preset];
        if (((_a = props.canvas3d.postprocessing.occlusion) === null || _a === void 0 ? void 0 : _a.name) === 'on') {
            props.canvas3d.postprocessing.occlusion.params.radius = this.radius;
            props.canvas3d.postprocessing.occlusion.params.bias = this.bias;
        }
        commands_1.PluginCommands.Canvas3D.SetSettings(this.plugin, {
            settings: {
                ...props,
                renderer: {
                    ...this.plugin.canvas3d.props.renderer,
                    ...props.canvas3d.renderer
                },
                postprocessing: {
                    ...this.plugin.canvas3d.props.postprocessing,
                    ...props.canvas3d.postprocessing
                },
            }
        });
    }
    async load({ url, format = 'mmcif', isBinary = true, assemblyId = '' }, radius, bias) {
        await this.plugin.clear();
        const data = await this.plugin.builders.data.download({ url: assets_1.Asset.Url(url), isBinary }, { state: { isGhost: true } });
        const trajectory = await this.plugin.builders.structure.parseTrajectory(data, format);
        const model = await this.plugin.builders.structure.createModel(trajectory);
        const structure = await this.plugin.builders.structure.createStructure(model, assemblyId ? { name: 'assembly', params: { id: assemblyId } } : { name: 'model', params: {} });
        const polymer = await this.plugin.builders.structure.tryCreateComponentStatic(structure, 'polymer');
        if (polymer)
            await this.plugin.builders.structure.representation.addRepresentation(polymer, { type: 'spacefill', color: 'illustrative' });
        const ligand = await this.plugin.builders.structure.tryCreateComponentStatic(structure, 'ligand');
        if (ligand)
            await this.plugin.builders.structure.representation.addRepresentation(ligand, { type: 'ball-and-stick', color: 'element-symbol', colorParams: { carbonColor: { name: 'element-symbol', params: {} } } });
        this.radius = radius;
        this.bias = bias;
        this.setPreset(this.preset);
    }
}
window.LightingDemo = new LightingDemo();
