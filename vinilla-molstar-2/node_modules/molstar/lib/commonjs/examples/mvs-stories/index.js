"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const jsx_runtime_1 = require("react/jsx-runtime");
/**
 * Copyright (c) 2025 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
const download_1 = require("../../mol-util/download");
const rxjs_1 = require("rxjs");
const stories_1 = require("./stories");
const use_behavior_1 = require("../../mol-plugin-ui/hooks/use-behavior");
const client_1 = require("react-dom/client");
const context_1 = require("../../apps/mvs-stories/context");
require("../../apps/mvs-stories/elements");
require("./favicon.ico");
require("../../mol-plugin-ui/skin/light.scss");
require("../../apps/mvs-stories/styles.scss");
require("./index.html");
function getContext(name) {
    return (0, context_1.getMVSStoriesContext)({ name });
}
const CurrentStory = new rxjs_1.BehaviorSubject(undefined);
function SelectStoryUI({ subject }) {
    const current = (0, use_behavior_1.useBehavior)(subject);
    const selectedId = (current === null || current === void 0 ? void 0 : current.kind) === 'built-in' ? current.id : (current === null || current === void 0 ? void 0 : current.kind) === 'url' ? 'url' : '';
    return (0, jsx_runtime_1.jsxs)("select", { onChange: e => {
            const value = e.currentTarget.value;
            const s = stories_1.Stories.find(s => s.id === value);
            if (!s)
                return;
            subject.next({ kind: 'built-in', id: s.id });
        }, value: selectedId, children: [!current && (0, jsx_runtime_1.jsx)("option", { value: '', children: "Select a story..." }), stories_1.Stories.map(s => (0, jsx_runtime_1.jsxs)("option", { value: s.id, children: ["Story: ", s.name] }, s.name)), (current === null || current === void 0 ? void 0 : current.kind) === 'url' && (0, jsx_runtime_1.jsx)("option", { disabled: true, children: "------------------" }), (current === null || current === void 0 ? void 0 : current.kind) === 'url' && (0, jsx_runtime_1.jsx)("option", { value: 'url', children: current.url })] });
}
function init() {
    CurrentStory.subscribe(story => {
        if (!story) {
            history.replaceState({}, '', '');
        }
        else if (story.kind === 'url') {
            history.replaceState({}, '', story ? `?story-url=${encodeURIComponent(story.url)}&data-format=${story.format}` : '');
            getContext().dispatch({
                kind: 'load-mvs',
                format: story.format,
                url: story.url,
            });
        }
        else if (story.kind === 'built-in') {
            history.replaceState({}, '', story ? `?story=${story.id}` : '');
            const s = stories_1.Stories.find(s => s.id === story.id);
            if (s) {
                getContext().dispatch({
                    kind: 'load-mvs',
                    data: s.buildStory(),
                });
            }
            else {
                console.warn('Story not found:', story.id);
                CurrentStory.next({ kind: 'built-in', id: stories_1.Stories[0].id });
            }
        }
    });
    const urlParams = new URLSearchParams(window.location.search);
    const storyUrl = urlParams.get('story-url');
    const dataFormat = urlParams.get('data-format');
    const storyId = urlParams.get('story');
    if (storyUrl) {
        CurrentStory.next({ kind: 'url', url: storyUrl, format: dataFormat !== null && dataFormat !== void 0 ? dataFormat : 'mvsj' });
    }
    else if (storyId) {
        CurrentStory.next({ kind: 'built-in', id: storyId });
    }
    else {
        CurrentStory.next({ kind: 'built-in', id: stories_1.Stories[0].id });
    }
    (0, client_1.createRoot)(document.getElementById('select-story')).render((0, jsx_runtime_1.jsx)(SelectStoryUI, { subject: CurrentStory }));
}
window.downloadStory = () => {
    var _a;
    if (((_a = CurrentStory.value) === null || _a === void 0 ? void 0 : _a.kind) !== 'built-in')
        return;
    const id = CurrentStory.value.id;
    const story = stories_1.Stories.find(s => s.id === id);
    if (!story)
        return;
    const data = JSON.stringify(story.buildStory(), null, 2);
    (0, download_1.download)(new Blob([data], { type: 'application/json' }), `${id}-story.mvsj`);
};
window.initStories = init;
window.CurrentStory = CurrentStory;
