/**
 * Copyright (c) 2025 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
import { buildStory as kinase } from './kinase';
import { buildStory as tbp } from './tbp';
export declare const Stories: readonly [{
    readonly id: "kinase";
    readonly name: "BCR-ABL: A Kinase Out of Control";
    readonly buildStory: typeof kinase;
}, {
    readonly id: "tata";
    readonly name: "TATA-Binding Protein and its Role in Transcription Initiation ";
    readonly buildStory: typeof tbp;
}];
