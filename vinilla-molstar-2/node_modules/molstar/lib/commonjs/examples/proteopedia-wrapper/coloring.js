"use strict";
/**
 * Copyright (c) 2019 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createProteopediaCustomTheme = createProteopediaCustomTheme;
const structure_1 = require("../../mol-model/structure");
const color_1 = require("../../mol-util/color");
const param_definition_1 = require("../../mol-util/param-definition");
const Description = 'Gives every chain a color from a list based on its `asym_id` value.';
function createProteopediaCustomTheme(colors) {
    const ProteopediaCustomColorThemeParams = {
        colors: param_definition_1.ParamDefinition.ObjectList({ color: param_definition_1.ParamDefinition.Color((0, color_1.Color)(0xffffff)) }, ({ color }) => color_1.Color.toHexString(color), { defaultValue: colors.map(c => ({ color: (0, color_1.Color)(c) })) })
    };
    function getChainIdColorThemeParams(ctx) {
        return ProteopediaCustomColorThemeParams; // TODO return copy
    }
    function getAsymId(unit) {
        switch (unit.kind) {
            case structure_1.Unit.Kind.Atomic:
                return structure_1.StructureProperties.chain.label_asym_id;
            case structure_1.Unit.Kind.Spheres:
            case structure_1.Unit.Kind.Gaussians:
                return structure_1.StructureProperties.coarse.asym_id;
        }
    }
    function addAsymIds(map, data) {
        let j = map.size;
        for (let o = 0, ol = data.rowCount; o < ol; ++o) {
            const k = data.value(o);
            if (!map.has(k)) {
                map.set(k, j);
                j += 1;
            }
        }
    }
    function ProteopediaCustomColorTheme(ctx, props) {
        let color;
        const colors = props.colors, colorCount = colors.length, defaultColor = colors[0].color;
        if (ctx.structure) {
            const l = structure_1.StructureElement.Location.create(ctx.structure);
            const { models } = ctx.structure;
            const asymIdSerialMap = new Map();
            for (let i = 0, il = models.length; i < il; ++i) {
                const m = models[i];
                addAsymIds(asymIdSerialMap, m.atomicHierarchy.chains.label_asym_id);
                if (m.coarseHierarchy.isDefined) {
                    addAsymIds(asymIdSerialMap, m.coarseHierarchy.spheres.asym_id);
                    addAsymIds(asymIdSerialMap, m.coarseHierarchy.gaussians.asym_id);
                }
            }
            color = (location) => {
                if (structure_1.StructureElement.Location.is(location)) {
                    const asym_id = getAsymId(location.unit);
                    const o = asymIdSerialMap.get(asym_id(location)) || 0;
                    return colors[o % colorCount].color;
                }
                else if (structure_1.Bond.isLocation(location)) {
                    const asym_id = getAsymId(location.aUnit);
                    l.unit = location.aUnit;
                    l.element = location.aUnit.elements[location.aIndex];
                    const o = asymIdSerialMap.get(asym_id(l)) || 0;
                    return colors[o % colorCount].color;
                }
                return defaultColor;
            };
        }
        else {
            color = () => defaultColor;
        }
        return {
            factory: ProteopediaCustomColorTheme,
            granularity: 'group',
            color,
            props,
            description: Description,
            legend: undefined
        };
    }
    return {
        name: 'proteopedia-custom',
        label: 'Proteopedia Custom',
        category: 'Custom',
        factory: ProteopediaCustomColorTheme,
        getParams: getChainIdColorThemeParams,
        defaultValues: param_definition_1.ParamDefinition.getDefaultValues(ProteopediaCustomColorThemeParams),
        isApplicable: (ctx) => !!ctx.structure
    };
}
