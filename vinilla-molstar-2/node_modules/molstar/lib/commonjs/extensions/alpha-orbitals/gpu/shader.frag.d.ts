/**
 * Copyright (c) 2020 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
export declare const UTILS = "\nfloat L1(vec3 p, float a0, float a1, float a2) {\n    return a0 * p.z + a1 * p.x + a2 * p.y;\n}\n\nfloat L2(vec3 p, float a0, float a1, float a2, float a3, float a4) {\n    float x = p.x, y = p.y, z = p.z;\n    float xx = x * x, yy = y * y, zz = z * z;\n    return (\n        a0 * (-0.5 * xx - 0.5 * yy + zz) +\n        a1 * (1.7320508075688772 * x * z) +\n        a2 * (1.7320508075688772 * y * z) +\n        a3 * (0.8660254037844386 * xx - 0.8660254037844386 * yy) +\n        a4 * (1.7320508075688772 * x * y)\n    );\n}\n\nfloat L3(vec3 p, float a0, float a1, float a2, float a3, float a4, float a5, float a6) {\n    float x = p.x, y = p.y, z = p.z;\n    float xx = x * x, yy = y * y, zz = z * z;\n    float xxx = xx * x, yyy = yy * y, zzz = zz * z;\n    return (\n        a0 * (-1.5 * xx * z - 1.5 * yy * z + zzz) +\n        a1 * (-0.6123724356957945 * xxx - 0.6123724356957945 * x * yy + 2.449489742783178 * x * zz) +\n        a2 * (-0.6123724356957945 * xx * y - 0.6123724356957945 * yyy + 2.449489742783178 * y * zz) +\n        a3 * (1.9364916731037085 * xx * z - 1.9364916731037085 * yy * z) +\n        a4 * (3.872983346207417 * x * y * z) +\n        a5 * (0.7905694150420949 * xxx - 2.3717082451262845 * x * yy) +\n        a6 * (2.3717082451262845 * xx * y - 0.7905694150420949 * yyy)\n    );\n}\n\nfloat L4(vec3 p, float a0, float a1, float a2, float a3, float a4, float a5, float a6, float a7, float a8) {\n    float x = p.x, y = p.y, z = p.z;\n    float xx = x * x, yy = y * y, zz = z * z;\n    float xxx = xx * x, yyy = yy * y, zzz = zz * z;\n    float xxxx = xxx * x, yyyy = yyy * y, zzzz = zzz * z;\n    return (\n        a0 * (0.375 * xxxx + 0.75 * xx * yy + 0.375 * yyyy - 3.0 * xx * zz - 3.0 * yy * zz + zzzz) +\n        a1 * (-2.3717082451262845 * xxx * z - 2.3717082451262845 * x * yy * z + 3.1622776601683795 * x * zzz) +\n        a2 * (-2.3717082451262845 * xx * y * z - 2.3717082451262845 * yyy * z + 3.1622776601683795 * y * zzz) +\n        a3 * (-0.5590169943749475 * xxxx + 0.5590169943749475 * yyyy + 3.3541019662496847 * xx * zz - 3.3541019662496847 * yy * zz) +\n        a4 * (-1.118033988749895 * xxx * y - 1.118033988749895 * x * yyy + 6.708203932499369 * x * y * zz) +\n        a5 * (2.091650066335189 * xxx * z + -6.274950199005566 * x * yy * z) +\n        a6 * (6.274950199005566 * xx * y * z + -2.091650066335189 * yyy * z) +\n        a7 * (0.739509972887452 * xxxx - 4.437059837324712 * xx * yy + 0.739509972887452 * yyyy) +\n        a8 * (2.958039891549808 * xxx * y + -2.958039891549808 * x * yyy)\n    );\n}\n\nfloat alpha(float offset, float f) {\n    #ifdef WEBGL1\n        // in webgl1, the value is in the alpha channel!\n        return texture2D(tAlpha, vec2(offset * f, 0.5)).a;\n    #else\n        return texture2D(tAlpha, vec2(offset * f, 0.5)).x;\n    #endif\n}\n\nfloat Y(int L, vec3 X, float aO, float fA) {\n    if (L == 0) {\n        return alpha(aO, fA);\n    } else if (L == 1) {\n        return L1(X,\n            alpha(aO, fA), alpha(aO + 1.0, fA), alpha(aO + 2.0, fA)\n        );\n    } else if (L == 2) {\n        return L2(X,\n            alpha(aO, fA), alpha(aO + 1.0, fA), alpha(aO + 2.0, fA), alpha(aO + 3.0, fA), alpha(aO + 4.0, fA)\n        );\n    } else if (L == 3) {\n        return L3(X,\n            alpha(aO, fA), alpha(aO + 1.0, fA), alpha(aO + 2.0, fA), alpha(aO + 3.0, fA), alpha(aO + 4.0, fA),\n            alpha(aO + 5.0, fA), alpha(aO + 6.0, fA)\n        );\n    } else if (L == 4) {\n        return L4(X,\n            alpha(aO, fA), alpha(aO + 1.0, fA), alpha(aO + 2.0, fA), alpha(aO + 3.0, fA), alpha(aO + 4.0, fA),\n            alpha(aO + 5.0, fA), alpha(aO + 6.0, fA), alpha(aO + 7.0, fA), alpha(aO + 8.0, fA)\n        );\n    }\n    // TODO: do we need L > 4?\n    return 0.0;\n}\n\n#ifndef WEBGL1\n    float R(float R2, int start, int end, float fCoeff) {\n        float gauss = 0.0;\n        for (int i = start; i < end; i++) {\n            vec2 c = texture2D(tCoeff, vec2(float(i) * fCoeff, 0.5)).xy;\n            gauss += c.x * exp(-c.y * R2);\n        }\n        return gauss;\n    }\n#else\n    float R(float R2, int start, int end, float fCoeff) {\n        float gauss = 0.0;\n        int o = start;\n        for (int i = 0; i < uMaxCoeffs; i++) {\n            if (o >= end) break;\n\n            vec2 c = texture2D(tCoeff, vec2(float(o) * fCoeff, 0.5)).xy;\n            gauss += c.x * exp(-c.y * R2);\n            o++;\n        }\n        return gauss;\n    }\n#endif\n";
export declare const MAIN = "\n    float fCenter = 1.0 / float(uNCenters - 1);\n    float fCoeff = 1.0 / float(uNCoeff - 1);\n    float fA = 1.0 / float(uNAlpha - 1);\n\n    float v = 0.0;\n\n    for (int i = 0; i < uNCenters; i++) {\n        vec2 cCoord = vec2(float(i) * fCenter, 0.5);\n\n        vec4 center = texture2D(tCenters, cCoord);\n        vec3 X = xyz - center.xyz;\n        float R2 = dot(X, X);\n\n        // center.w is squared cutoff radius\n        if (R2 > center.w) {\n            continue;\n        }\n\n        vec4 info = texture2D(tInfo, cCoord);\n\n        int L = int(info.x);\n        float aO = info.y;\n        int coeffStart = int(info.z);\n        int coeffEnd = int(info.w);\n\n        v += R(R2, coeffStart, coeffEnd, fCoeff) * Y(L, X, aO, fA);\n    }\n";
