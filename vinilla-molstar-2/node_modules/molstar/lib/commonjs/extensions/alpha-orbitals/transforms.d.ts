/**
 * Copyright (c) 2020 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
import { PluginStateObject } from '../../mol-plugin-state/objects';
import { ParamDefinition as PD } from '../../mol-util/param-definition';
import { SphericalBasisOrder } from './spherical-functions';
import { StateTransformer } from '../../mol-state';
import { AlphaOrbital, Basis } from './data-model';
declare const BasisAndOrbitals_base: {
    new (data: {
        basis: Basis;
        order: SphericalBasisOrder;
        orbitals: AlphaOrbital[];
    }, props?: {
        label: string;
        description?: string;
    } | undefined): {
        id: import("../../mol-util").UUID;
        type: PluginStateObject.TypeInfo;
        label: string;
        description?: string;
        data: {
            basis: Basis;
            order: SphericalBasisOrder;
            orbitals: AlphaOrbital[];
        };
    };
    type: PluginStateObject.TypeInfo;
    is(obj?: import("../../mol-state").StateObject): obj is import("../../mol-state").StateObject<{
        basis: Basis;
        order: SphericalBasisOrder;
        orbitals: AlphaOrbital[];
    }, PluginStateObject.TypeInfo>;
};
export declare class BasisAndOrbitals extends BasisAndOrbitals_base {
}
export declare const StaticBasisAndOrbitals: StateTransformer<PluginStateObject.Root, BasisAndOrbitals, PD.Normalize<{
    label: string;
    basis: Basis;
    order: SphericalBasisOrder;
    orbitals: AlphaOrbital[];
}>>;
export declare const CreateOrbitalVolume: StateTransformer<BasisAndOrbitals, PluginStateObject.Volume.Data, PD.Normalize<{
    cutoffThreshold: number;
    boxExpand: number;
    gridSpacing: PD.Normalize<{
        atomCount: number;
        spacing: number;
    }>[];
    clampValues: PD.NamedParams<PD.Normalize<unknown>, "off"> | PD.NamedParams<PD.Normalize<{
        sigma: /*elided*/ any;
    }>, "on">;
    index: number;
}>>;
export declare const CreateOrbitalDensityVolume: StateTransformer<BasisAndOrbitals, PluginStateObject.Volume.Data, PD.Normalize<{
    cutoffThreshold: number;
    boxExpand: number;
    gridSpacing: PD.Normalize<{
        atomCount: number;
        spacing: number;
    }>[];
    clampValues: PD.NamedParams<PD.Normalize<unknown>, "off"> | PD.NamedParams<PD.Normalize<{
        sigma: /*elided*/ any;
    }>, "on">;
}>>;
export declare const CreateOrbitalRepresentation3D: StateTransformer<PluginStateObject.Volume.Data, PluginStateObject.Volume.Representation3D, PD.Normalize<{
    relativeIsovalue: number;
    kind: "negative" | "positive";
    color: import("../../mol-util/color").Color;
    alpha: number;
    xrayShaded: boolean;
    pickable: boolean;
    tryUseGpu: boolean;
}>>;
export {};
