/**
 * Copyright (c) 2020-2024 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 */
import { ParamDefinition as PD } from '../../mol-util/param-definition';
import { CustomStructureProperty } from '../../mol-model-props/common/custom-structure-property';
import { Vec3 } from '../../mol-math/linear-algebra';
import { QuerySymbolRuntime } from '../../mol-script/runtime/query/base';
export declare const MembraneOrientationParams: {
    numberOfSpherePoints: PD.Numeric;
    stepSize: PD.Numeric;
    minThickness: PD.Numeric;
    maxThickness: PD.Numeric;
    asaCutoff: PD.Numeric;
    adjust: PD.Numeric;
    tmdetDefinition: PD.BooleanParam;
};
export type MembraneOrientationParams = typeof MembraneOrientationParams;
export type MembraneOrientationProps = PD.Values<MembraneOrientationParams>;
export { MembraneOrientation };
interface MembraneOrientation {
    readonly planePoint1: Vec3;
    readonly planePoint2: Vec3;
    readonly normalVector: Vec3;
    readonly radius: number;
    readonly centroid: Vec3;
}
declare namespace MembraneOrientation {
    enum Tag {
        Representation = "membrane-orientation-3d"
    }
    const symbols: {
        isTransmembrane: QuerySymbolRuntime;
    };
}
export declare const MembraneOrientationProvider: CustomStructureProperty.Provider<MembraneOrientationParams, MembraneOrientation>;
