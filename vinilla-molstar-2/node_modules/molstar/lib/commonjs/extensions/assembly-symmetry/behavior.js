"use strict";
/**
 * Copyright (c) 2018-2020 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssemblySymmetryConfig = exports.AssemblySymmetryPreset = exports.AssemblySymmetry3D = exports.InitAssemblySymmetry3D = exports.AssemblySymmetry = void 0;
exports.tryCreateAssemblySymmetry = tryCreateAssemblySymmetry;
exports.getAssemblySymmetryConfig = getAssemblySymmetryConfig;
const param_definition_1 = require("../../mol-util/param-definition");
const prop_1 = require("./prop");
const behavior_1 = require("../../mol-plugin/behavior/behavior");
const representation_1 = require("./representation");
const color_1 = require("./color");
const objects_1 = require("../../mol-plugin-state/objects");
const mol_task_1 = require("../../mol-task");
const config_1 = require("../../mol-plugin/config");
const mol_state_1 = require("../../mol-state");
const ui_1 = require("./ui");
const representation_preset_1 = require("../../mol-plugin-state/builder/structure/representation-preset");
const Tag = prop_1.AssemblySymmetryData.Tag;
exports.AssemblySymmetry = behavior_1.PluginBehavior.create({
    name: 'assembly-symmetry-prop',
    category: 'custom-props',
    display: {
        name: 'Assembly Symmetry',
        description: 'Assembly Symmetry data provided by RCSB PDB (calculated with BioJava) or by PDBe.'
    },
    ctor: class extends behavior_1.PluginBehavior.Handler {
        constructor() {
            super(...arguments);
            this.provider = prop_1.AssemblySymmetryProvider;
        }
        register() {
            this.ctx.state.data.actions.add(exports.InitAssemblySymmetry3D);
            this.ctx.customStructureProperties.register(this.provider, this.params.autoAttach);
            this.ctx.representation.structure.themes.colorThemeRegistry.add(color_1.AssemblySymmetryClusterColorThemeProvider);
            this.ctx.genericRepresentationControls.set(Tag.Representation, selection => {
                const refs = [];
                selection.structures.forEach(structure => {
                    var _a;
                    const symmRepr = (_a = structure.genericRepresentations) === null || _a === void 0 ? void 0 : _a.filter(r => r.cell.transform.transformer.id === AssemblySymmetry3D.id)[0];
                    if (symmRepr)
                        refs.push(symmRepr);
                });
                return [refs, 'Symmetries'];
            });
            this.ctx.customStructureControls.set(Tag.Representation, ui_1.AssemblySymmetryControls);
            this.ctx.builders.structure.representation.registerPreset(exports.AssemblySymmetryPreset);
        }
        update(p) {
            const updated = this.params.autoAttach !== p.autoAttach;
            this.params.autoAttach = p.autoAttach;
            this.ctx.customStructureProperties.setDefaultAutoAttach(this.provider.descriptor.name, this.params.autoAttach);
            return updated;
        }
        unregister() {
            this.ctx.state.data.actions.remove(exports.InitAssemblySymmetry3D);
            this.ctx.customStructureProperties.unregister(this.provider.descriptor.name);
            this.ctx.representation.structure.themes.colorThemeRegistry.remove(color_1.AssemblySymmetryClusterColorThemeProvider);
            this.ctx.genericRepresentationControls.delete(Tag.Representation);
            this.ctx.customStructureControls.delete(Tag.Representation);
            this.ctx.builders.structure.representation.unregisterPreset(exports.AssemblySymmetryPreset);
        }
    },
    params: () => ({
        autoAttach: param_definition_1.ParamDefinition.Boolean(false),
        serverUrl: param_definition_1.ParamDefinition.Text(prop_1.AssemblySymmetryData.DefaultServerUrl)
    })
});
//
exports.InitAssemblySymmetry3D = mol_state_1.StateAction.build({
    display: {
        name: 'Assembly Symmetry',
        description: 'Initialize Assembly Symmetry axes and cage. Data provided by RCSB PDB (calculated with BioJava) or by PDBe.'
    },
    from: objects_1.PluginStateObject.Molecule.Structure,
    isApplicable: (a) => prop_1.AssemblySymmetryData.isApplicable(a.data),
    params: (a, plugin) => getConfiguredDefaultParams(plugin)
})(({ a, ref, state, params }, plugin) => mol_task_1.Task.create('Init Assembly Symmetry', async (ctx) => {
    try {
        const propCtx = { runtime: ctx, assetManager: plugin.managers.asset, errorContext: plugin.errorContext };
        await prop_1.AssemblySymmetryDataProvider.attach(propCtx, a.data, params);
        const assemblySymmetryData = prop_1.AssemblySymmetryDataProvider.get(a.data).value;
        const symmetryIndex = assemblySymmetryData ? prop_1.AssemblySymmetryData.firstNonC1(assemblySymmetryData) : -1;
        await prop_1.AssemblySymmetryProvider.attach(propCtx, a.data, { ...params, symmetryIndex });
    }
    catch (e) {
        plugin.log.error(`Assembly Symmetry: ${e}`);
        return;
    }
    const tree = state.build().to(ref)
        .applyOrUpdateTagged(prop_1.AssemblySymmetryData.Tag.Representation, AssemblySymmetry3D);
    await state.updateTree(tree).runInContext(ctx);
}));
const AssemblySymmetry3D = objects_1.PluginStateTransform.BuiltIn({
    name: Tag.Representation,
    display: {
        name: 'Assembly Symmetry',
        description: 'Assembly Symmetry axes and cage. Data provided by RCSB PDB (calculated with BioJava) or by PDBe.'
    },
    from: objects_1.PluginStateObject.Molecule.Structure,
    to: objects_1.PluginStateObject.Shape.Representation3D,
    params: (a) => {
        return {
            ...representation_1.AssemblySymmetryParams,
        };
    }
})({
    canAutoUpdate({ oldParams, newParams }) {
        return true;
    },
    apply({ a, params }, plugin) {
        return mol_task_1.Task.create('Assembly Symmetry', async (ctx) => {
            var _a;
            await prop_1.AssemblySymmetryProvider.attach({ runtime: ctx, assetManager: plugin.managers.asset, errorContext: plugin.errorContext }, a.data);
            const assemblySymmetry = prop_1.AssemblySymmetryProvider.get(a.data).value;
            if (!assemblySymmetry || assemblySymmetry.symbol === 'C1') {
                return mol_state_1.StateObject.Null;
            }
            const repr = (0, representation_1.AssemblySymmetryRepresentation)({ webgl: (_a = plugin.canvas3d) === null || _a === void 0 ? void 0 : _a.webgl, ...plugin.representation.structure.themes }, () => representation_1.AssemblySymmetryParams);
            await repr.createOrUpdate(params, a.data).runInContext(ctx);
            const { type, kind, symbol } = assemblySymmetry;
            return new objects_1.PluginStateObject.Shape.Representation3D({ repr, sourceData: a.data }, { label: kind, description: `${type} (${symbol})` });
        });
    },
    update({ a, b, newParams }, plugin) {
        return mol_task_1.Task.create('Assembly Symmetry', async (ctx) => {
            await prop_1.AssemblySymmetryProvider.attach({ runtime: ctx, assetManager: plugin.managers.asset, errorContext: plugin.errorContext }, a.data);
            const assemblySymmetry = prop_1.AssemblySymmetryProvider.get(a.data).value;
            if (!assemblySymmetry || assemblySymmetry.symbol === 'C1') {
                // this should NOT be StateTransformer.UpdateResult.Null
                // because that keeps the old object
                return mol_state_1.StateTransformer.UpdateResult.Recreate;
            }
            const props = { ...b.data.repr.props, ...newParams };
            await b.data.repr.createOrUpdate(props, a.data).runInContext(ctx);
            b.data.sourceData = a.data;
            const { type, kind, symbol } = assemblySymmetry;
            b.label = kind;
            b.description = `${type} (${symbol})`;
            return mol_state_1.StateTransformer.UpdateResult.Updated;
        });
    },
    isApplicable(a) {
        return prop_1.AssemblySymmetryData.isApplicable(a.data);
    }
});
exports.AssemblySymmetry3D = AssemblySymmetry3D;
//
exports.AssemblySymmetryPreset = (0, representation_preset_1.StructureRepresentationPresetProvider)({
    id: 'preset-structure-representation-assembly-symmetry',
    display: {
        name: 'Assembly Symmetry', group: 'Annotation',
        description: 'Shows Assembly Symmetry axes and cage; colors structure according to assembly symmetry cluster membership. Data provided by RCSB PDB (calculated with BioJava) or by PDBe.'
    },
    isApplicable(a) {
        return prop_1.AssemblySymmetryData.isApplicable(a.data);
    },
    params: (a, plugin) => {
        return {
            ...representation_preset_1.StructureRepresentationPresetProvider.CommonParams,
            ...getConfiguredDefaultParams(plugin)
        };
    },
    async apply(ref, params, plugin) {
        var _a;
        const structureCell = mol_state_1.StateObjectRef.resolveAndCheck(plugin.state.data, ref);
        const structure = (_a = structureCell === null || structureCell === void 0 ? void 0 : structureCell.obj) === null || _a === void 0 ? void 0 : _a.data;
        if (!structureCell || !structure)
            return {};
        if (!prop_1.AssemblySymmetryDataProvider.get(structure).value) {
            await plugin.runTask(mol_task_1.Task.create('Assembly Symmetry', async (runtime) => {
                const propCtx = { runtime, assetManager: plugin.managers.asset, errorContext: plugin.errorContext };
                const propProps = { serverType: params.serverType, serverUrl: params.serverUrl };
                await prop_1.AssemblySymmetryDataProvider.attach(propCtx, structure, propProps);
                const assemblySymmetryData = prop_1.AssemblySymmetryDataProvider.get(structure).value;
                const symmetryIndex = assemblySymmetryData ? prop_1.AssemblySymmetryData.firstNonC1(assemblySymmetryData) : -1;
                await prop_1.AssemblySymmetryProvider.attach(propCtx, structure, { ...propProps, symmetryIndex });
            }));
        }
        const assemblySymmetry = await tryCreateAssemblySymmetry(plugin, structureCell);
        const colorTheme = getAssemblySymmetryConfig(plugin).ApplyColors && assemblySymmetry.isOk ? Tag.Cluster : undefined;
        const preset = await representation_preset_1.PresetStructureRepresentations.auto.apply(ref, { ...params, theme: { globalName: colorTheme, focus: { name: colorTheme } } }, plugin);
        return { components: preset.components, representations: { ...preset.representations, assemblySymmetry } };
    }
});
function tryCreateAssemblySymmetry(plugin, structure, params, initialState) {
    const state = plugin.state.data;
    const assemblySymmetry = state.build().to(structure)
        .applyOrUpdateTagged(prop_1.AssemblySymmetryData.Tag.Representation, AssemblySymmetry3D, params, { state: initialState });
    return assemblySymmetry.commit({ revertOnError: true });
}
//
exports.AssemblySymmetryConfig = {
    DefaultServerType: new config_1.PluginConfigItem('assembly-symmetry.server-type', prop_1.AssemblySymmetryDataParams.serverType.defaultValue),
    DefaultServerUrl: new config_1.PluginConfigItem('assembly-symmetry.server-url', prop_1.AssemblySymmetryDataParams.serverUrl.defaultValue),
    ApplyColors: new config_1.PluginConfigItem('assembly-symmetry.apply-colors', true),
};
function getAssemblySymmetryConfig(plugin) {
    var _a, _b, _c, _d, _e, _f;
    return {
        ApplyColors: (_b = (_a = plugin.config.get(exports.AssemblySymmetryConfig.ApplyColors)) !== null && _a !== void 0 ? _a : exports.AssemblySymmetryConfig.ApplyColors.defaultValue) !== null && _b !== void 0 ? _b : true,
        DefaultServerType: (_d = (_c = plugin.config.get(exports.AssemblySymmetryConfig.DefaultServerType)) !== null && _c !== void 0 ? _c : exports.AssemblySymmetryConfig.DefaultServerType.defaultValue) !== null && _d !== void 0 ? _d : prop_1.AssemblySymmetryDataParams.serverType.defaultValue,
        DefaultServerUrl: (_f = (_e = plugin.config.get(exports.AssemblySymmetryConfig.DefaultServerUrl)) !== null && _e !== void 0 ? _e : exports.AssemblySymmetryConfig.DefaultServerUrl.defaultValue) !== null && _f !== void 0 ? _f : prop_1.AssemblySymmetryDataParams.serverUrl.defaultValue,
    };
}
function getConfiguredDefaultParams(plugin) {
    const config = getAssemblySymmetryConfig(plugin);
    const params = param_definition_1.ParamDefinition.clone(prop_1.AssemblySymmetryDataParams);
    param_definition_1.ParamDefinition.setDefaultValues(params, { serverType: config.DefaultServerType, serverUrl: config.DefaultServerUrl });
    return params;
}
