"use strict";
/**
 * Copyright (c) 2018-2020 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssemblySymmetryClusterColorThemeProvider = exports.AssemblySymmetryClusterColorThemeParams = void 0;
exports.getAssemblySymmetryClusterColorThemeParams = getAssemblySymmetryClusterColorThemeParams;
exports.AssemblySymmetryClusterColorTheme = AssemblySymmetryClusterColorTheme;
const param_definition_1 = require("../../mol-util/param-definition");
const prop_1 = require("./prop");
const color_1 = require("../../mol-util/color");
const structure_1 = require("../../mol-model/structure");
const palette_1 = require("../../mol-util/color/palette");
const categories_1 = require("../../mol-theme/color/categories");
const DefaultColor = (0, color_1.Color)(0xCCCCCC);
function getAsymId(unit) {
    switch (unit.kind) {
        case structure_1.Unit.Kind.Atomic:
            return structure_1.StructureProperties.chain.label_asym_id;
        case structure_1.Unit.Kind.Spheres:
        case structure_1.Unit.Kind.Gaussians:
            return structure_1.StructureProperties.coarse.asym_id;
    }
}
function clusterMemberKey(asymId, operList) {
    return `${asymId}-${operList.join('|')}`;
}
exports.AssemblySymmetryClusterColorThemeParams = {
    ...(0, palette_1.getPaletteParams)({ colorList: 'red-yellow-blue' }),
};
function getAssemblySymmetryClusterColorThemeParams(ctx) {
    const params = param_definition_1.ParamDefinition.clone(exports.AssemblySymmetryClusterColorThemeParams);
    return params;
}
function AssemblySymmetryClusterColorTheme(ctx, props) {
    var _a;
    let color = () => DefaultColor;
    let legend;
    const assemblySymmetry = ctx.structure && prop_1.AssemblySymmetryProvider.get(ctx.structure);
    const contextHash = assemblySymmetry === null || assemblySymmetry === void 0 ? void 0 : assemblySymmetry.version;
    const clusters = (_a = assemblySymmetry === null || assemblySymmetry === void 0 ? void 0 : assemblySymmetry.value) === null || _a === void 0 ? void 0 : _a.clusters;
    if ((clusters === null || clusters === void 0 ? void 0 : clusters.length) && ctx.structure) {
        const l = structure_1.StructureElement.Location.create(ctx.structure);
        const clusterByMember = new Map();
        for (let i = 0, il = clusters.length; i < il; ++i) {
            const { members } = clusters[i];
            for (let j = 0, jl = members.length; j < jl; ++j) {
                const asymId = members[j].asym_id;
                const operList = [...members[j].pdbx_struct_oper_list_ids || []];
                clusterByMember.set(clusterMemberKey(asymId, operList), i);
                if (operList.length === 0) {
                    operList.push('1'); // TODO hack assuming '1' is the id of the identity operator
                    clusterByMember.set(clusterMemberKey(asymId, operList), i);
                }
            }
        }
        const palette = (0, palette_1.getPalette)(clusters.length, props);
        legend = palette.legend;
        const _emptyList = [];
        const getColor = (location) => {
            const { assembly } = location.unit.conformation.operator;
            const asymId = getAsymId(location.unit)(location);
            const cluster = clusterByMember.get(clusterMemberKey(asymId, (assembly === null || assembly === void 0 ? void 0 : assembly.operList) || _emptyList));
            return cluster !== undefined ? palette.color(cluster) : DefaultColor;
        };
        color = (location) => {
            if (structure_1.StructureElement.Location.is(location)) {
                return getColor(location);
            }
            else if (structure_1.Bond.isLocation(location)) {
                l.unit = location.aUnit;
                l.element = location.aUnit.elements[location.aIndex];
                return getColor(l);
            }
            return DefaultColor;
        };
    }
    return {
        factory: AssemblySymmetryClusterColorTheme,
        granularity: 'instance',
        color,
        props,
        contextHash,
        description: 'Assigns chain colors according to assembly symmetry cluster membership data provided by RCSB PDB (calculated with BioJava) or by PDBe.',
        legend
    };
}
exports.AssemblySymmetryClusterColorThemeProvider = {
    name: prop_1.AssemblySymmetryData.Tag.Cluster,
    label: 'Assembly Symmetry Cluster',
    category: categories_1.ColorThemeCategory.Symmetry,
    factory: AssemblySymmetryClusterColorTheme,
    getParams: getAssemblySymmetryClusterColorThemeParams,
    defaultValues: param_definition_1.ParamDefinition.getDefaultValues(exports.AssemblySymmetryClusterColorThemeParams),
    isApplicable: (ctx) => prop_1.AssemblySymmetryData.isApplicable(ctx.structure),
    ensureCustomProperties: {
        attach: (ctx, data) => data.structure ? prop_1.AssemblySymmetryProvider.attach(ctx, data.structure, void 0, true) : Promise.resolve(),
        detach: (data) => data.structure && prop_1.AssemblySymmetryProvider.ref(data.structure, false)
    }
};
