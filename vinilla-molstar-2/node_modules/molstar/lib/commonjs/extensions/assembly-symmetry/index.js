"use strict";
/**
 * Copyright (c) 2020 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssemblySymmetryConfig = exports.AssemblySymmetry = void 0;
var behavior_1 = require("./behavior");
Object.defineProperty(exports, "AssemblySymmetry", { enumerable: true, get: function () { return behavior_1.AssemblySymmetry; } });
Object.defineProperty(exports, "AssemblySymmetryConfig", { enumerable: true, get: function () { return behavior_1.AssemblySymmetryConfig; } });
