/**
 * Copyright (c) 2018-2022 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 */
import { Color, ColorMap } from '../../mol-util/color';
export declare const DefaultNtCClassColors: {
    A: number;
    B: number;
    BII: number;
    miB: number;
    Z: number;
    IC: number;
    OPN: number;
    SYN: number;
    N: number;
};
export declare const ErrorColor: Color;
export declare const NtCColors: ColorMap<{
    NANT_Upr: number;
    NANT_Lwr: number;
    AA00_Upr: number;
    AA00_Lwr: number;
    AA02_Upr: number;
    AA02_Lwr: number;
    AA03_Upr: number;
    AA03_Lwr: number;
    AA04_Upr: number;
    AA04_Lwr: number;
    AA08_Upr: number;
    AA08_Lwr: number;
    AA09_Upr: number;
    AA09_Lwr: number;
    AA01_Upr: number;
    AA01_Lwr: number;
    AA05_Upr: number;
    AA05_Lwr: number;
    AA06_Upr: number;
    AA06_Lwr: number;
    AA10_Upr: number;
    AA10_Lwr: number;
    AA11_Upr: number;
    AA11_Lwr: number;
    AA07_Upr: number;
    AA07_Lwr: number;
    AA12_Upr: number;
    AA12_Lwr: number;
    AA13_Upr: number;
    AA13_Lwr: number;
    AB01_Upr: number;
    AB01_Lwr: number;
    AB02_Upr: number;
    AB02_Lwr: number;
    AB03_Upr: number;
    AB03_Lwr: number;
    AB04_Upr: number;
    AB04_Lwr: number;
    AB05_Upr: number;
    AB05_Lwr: number;
    BA01_Upr: number;
    BA01_Lwr: number;
    BA05_Upr: number;
    BA05_Lwr: number;
    BA09_Upr: number;
    BA09_Lwr: number;
    BA08_Upr: number;
    BA08_Lwr: number;
    BA10_Upr: number;
    BA10_Lwr: number;
    BA13_Upr: number;
    BA13_Lwr: number;
    BA16_Upr: number;
    BA16_Lwr: number;
    BA17_Upr: number;
    BA17_Lwr: number;
    BB00_Upr: number;
    BB00_Lwr: number;
    BB01_Upr: number;
    BB01_Lwr: number;
    BB17_Upr: number;
    BB17_Lwr: number;
    BB02_Upr: number;
    BB02_Lwr: number;
    BB03_Upr: number;
    BB03_Lwr: number;
    BB11_Upr: number;
    BB11_Lwr: number;
    BB16_Upr: number;
    BB16_Lwr: number;
    BB04_Upr: number;
    BB04_Lwr: number;
    BB05_Upr: number;
    BB05_Lwr: number;
    BB07_Upr: number;
    BB07_Lwr: number;
    BB08_Upr: number;
    BB08_Lwr: number;
    BB10_Upr: number;
    BB10_Lwr: number;
    BB12_Upr: number;
    BB12_Lwr: number;
    BB13_Upr: number;
    BB13_Lwr: number;
    BB14_Upr: number;
    BB14_Lwr: number;
    BB15_Upr: number;
    BB15_Lwr: number;
    BB20_Upr: number;
    BB20_Lwr: number;
    IC01_Upr: number;
    IC01_Lwr: number;
    IC02_Upr: number;
    IC02_Lwr: number;
    IC03_Upr: number;
    IC03_Lwr: number;
    IC04_Upr: number;
    IC04_Lwr: number;
    IC05_Upr: number;
    IC05_Lwr: number;
    IC06_Upr: number;
    IC06_Lwr: number;
    IC07_Upr: number;
    IC07_Lwr: number;
    OP01_Upr: number;
    OP01_Lwr: number;
    OP02_Upr: number;
    OP02_Lwr: number;
    OP03_Upr: number;
    OP03_Lwr: number;
    OP04_Upr: number;
    OP04_Lwr: number;
    OP05_Upr: number;
    OP05_Lwr: number;
    OP06_Upr: number;
    OP06_Lwr: number;
    OP07_Upr: number;
    OP07_Lwr: number;
    OP08_Upr: number;
    OP08_Lwr: number;
    OP09_Upr: number;
    OP09_Lwr: number;
    OP10_Upr: number;
    OP10_Lwr: number;
    OP11_Upr: number;
    OP11_Lwr: number;
    OP12_Upr: number;
    OP12_Lwr: number;
    OP13_Upr: number;
    OP13_Lwr: number;
    OP14_Upr: number;
    OP14_Lwr: number;
    OP15_Upr: number;
    OP15_Lwr: number;
    OP16_Upr: number;
    OP16_Lwr: number;
    OP17_Upr: number;
    OP17_Lwr: number;
    OP18_Upr: number;
    OP18_Lwr: number;
    OP19_Upr: number;
    OP19_Lwr: number;
    OP20_Upr: number;
    OP20_Lwr: number;
    OP21_Upr: number;
    OP21_Lwr: number;
    OP22_Upr: number;
    OP22_Lwr: number;
    OP23_Upr: number;
    OP23_Lwr: number;
    OP24_Upr: number;
    OP24_Lwr: number;
    OP25_Upr: number;
    OP25_Lwr: number;
    OP26_Upr: number;
    OP26_Lwr: number;
    OP27_Upr: number;
    OP27_Lwr: number;
    OP28_Upr: number;
    OP28_Lwr: number;
    OP29_Upr: number;
    OP29_Lwr: number;
    OP30_Upr: number;
    OP30_Lwr: number;
    OP31_Upr: number;
    OP31_Lwr: number;
    OPS1_Upr: number;
    OPS1_Lwr: number;
    OP1S_Upr: number;
    OP1S_Lwr: number;
    AAS1_Upr: number;
    AAS1_Lwr: number;
    AB1S_Upr: number;
    AB1S_Lwr: number;
    AB2S_Upr: number;
    AB2S_Lwr: number;
    BB1S_Upr: number;
    BB1S_Lwr: number;
    BB2S_Upr: number;
    BB2S_Lwr: number;
    BBS1_Upr: number;
    BBS1_Lwr: number;
    ZZ01_Upr: number;
    ZZ01_Lwr: number;
    ZZ02_Upr: number;
    ZZ02_Lwr: number;
    ZZ1S_Upr: number;
    ZZ1S_Lwr: number;
    ZZ2S_Upr: number;
    ZZ2S_Lwr: number;
    ZZS1_Upr: number;
    ZZS1_Lwr: number;
    ZZS2_Upr: number;
    ZZS2_Lwr: number;
}>;
