"use strict";
/**
 * Copyright (c) 2018-2022 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.NtCColors = exports.ErrorColor = exports.DefaultNtCClassColors = void 0;
const color_1 = require("../../mol-util/color");
exports.DefaultNtCClassColors = {
    A: 0xFFC1C1,
    B: 0xC8CFFF,
    BII: 0x0059DA,
    miB: 0x3BE8FB,
    Z: 0x01F60E,
    IC: 0xFA5CFB,
    OPN: 0xE90000,
    SYN: 0xFFFF01,
    N: 0xF2F2F2,
};
exports.ErrorColor = (0, color_1.Color)(0xFFA10A);
exports.NtCColors = (0, color_1.ColorMap)({
    NANT_Upr: exports.DefaultNtCClassColors.N,
    NANT_Lwr: exports.DefaultNtCClassColors.N,
    AA00_Upr: exports.DefaultNtCClassColors.A,
    AA00_Lwr: exports.DefaultNtCClassColors.A,
    AA02_Upr: exports.DefaultNtCClassColors.A,
    AA02_Lwr: exports.DefaultNtCClassColors.A,
    AA03_Upr: exports.DefaultNtCClassColors.A,
    AA03_Lwr: exports.DefaultNtCClassColors.A,
    AA04_Upr: exports.DefaultNtCClassColors.A,
    AA04_Lwr: exports.DefaultNtCClassColors.A,
    AA08_Upr: exports.DefaultNtCClassColors.A,
    AA08_Lwr: exports.DefaultNtCClassColors.A,
    AA09_Upr: exports.DefaultNtCClassColors.A,
    AA09_Lwr: exports.DefaultNtCClassColors.A,
    AA01_Upr: exports.DefaultNtCClassColors.A,
    AA01_Lwr: exports.DefaultNtCClassColors.A,
    AA05_Upr: exports.DefaultNtCClassColors.A,
    AA05_Lwr: exports.DefaultNtCClassColors.A,
    AA06_Upr: exports.DefaultNtCClassColors.A,
    AA06_Lwr: exports.DefaultNtCClassColors.A,
    AA10_Upr: exports.DefaultNtCClassColors.A,
    AA10_Lwr: exports.DefaultNtCClassColors.A,
    AA11_Upr: exports.DefaultNtCClassColors.A,
    AA11_Lwr: exports.DefaultNtCClassColors.A,
    AA07_Upr: exports.DefaultNtCClassColors.A,
    AA07_Lwr: exports.DefaultNtCClassColors.A,
    AA12_Upr: exports.DefaultNtCClassColors.A,
    AA12_Lwr: exports.DefaultNtCClassColors.A,
    AA13_Upr: exports.DefaultNtCClassColors.A,
    AA13_Lwr: exports.DefaultNtCClassColors.A,
    AB01_Upr: exports.DefaultNtCClassColors.A,
    AB01_Lwr: exports.DefaultNtCClassColors.B,
    AB02_Upr: exports.DefaultNtCClassColors.A,
    AB02_Lwr: exports.DefaultNtCClassColors.B,
    AB03_Upr: exports.DefaultNtCClassColors.A,
    AB03_Lwr: exports.DefaultNtCClassColors.B,
    AB04_Upr: exports.DefaultNtCClassColors.A,
    AB04_Lwr: exports.DefaultNtCClassColors.B,
    AB05_Upr: exports.DefaultNtCClassColors.A,
    AB05_Lwr: exports.DefaultNtCClassColors.B,
    BA01_Upr: exports.DefaultNtCClassColors.B,
    BA01_Lwr: exports.DefaultNtCClassColors.A,
    BA05_Upr: exports.DefaultNtCClassColors.B,
    BA05_Lwr: exports.DefaultNtCClassColors.A,
    BA09_Upr: exports.DefaultNtCClassColors.B,
    BA09_Lwr: exports.DefaultNtCClassColors.A,
    BA08_Upr: exports.DefaultNtCClassColors.BII,
    BA08_Lwr: exports.DefaultNtCClassColors.A,
    BA10_Upr: exports.DefaultNtCClassColors.B,
    BA10_Lwr: exports.DefaultNtCClassColors.A,
    BA13_Upr: exports.DefaultNtCClassColors.BII,
    BA13_Lwr: exports.DefaultNtCClassColors.A,
    BA16_Upr: exports.DefaultNtCClassColors.BII,
    BA16_Lwr: exports.DefaultNtCClassColors.A,
    BA17_Upr: exports.DefaultNtCClassColors.BII,
    BA17_Lwr: exports.DefaultNtCClassColors.A,
    BB00_Upr: exports.DefaultNtCClassColors.B,
    BB00_Lwr: exports.DefaultNtCClassColors.B,
    BB01_Upr: exports.DefaultNtCClassColors.B,
    BB01_Lwr: exports.DefaultNtCClassColors.B,
    BB17_Upr: exports.DefaultNtCClassColors.B,
    BB17_Lwr: exports.DefaultNtCClassColors.B,
    BB02_Upr: exports.DefaultNtCClassColors.B,
    BB02_Lwr: exports.DefaultNtCClassColors.B,
    BB03_Upr: exports.DefaultNtCClassColors.B,
    BB03_Lwr: exports.DefaultNtCClassColors.B,
    BB11_Upr: exports.DefaultNtCClassColors.B,
    BB11_Lwr: exports.DefaultNtCClassColors.B,
    BB16_Upr: exports.DefaultNtCClassColors.B,
    BB16_Lwr: exports.DefaultNtCClassColors.B,
    BB04_Upr: exports.DefaultNtCClassColors.B,
    BB04_Lwr: exports.DefaultNtCClassColors.BII,
    BB05_Upr: exports.DefaultNtCClassColors.B,
    BB05_Lwr: exports.DefaultNtCClassColors.BII,
    BB07_Upr: exports.DefaultNtCClassColors.BII,
    BB07_Lwr: exports.DefaultNtCClassColors.BII,
    BB08_Upr: exports.DefaultNtCClassColors.BII,
    BB08_Lwr: exports.DefaultNtCClassColors.BII,
    BB10_Upr: exports.DefaultNtCClassColors.miB,
    BB10_Lwr: exports.DefaultNtCClassColors.miB,
    BB12_Upr: exports.DefaultNtCClassColors.miB,
    BB12_Lwr: exports.DefaultNtCClassColors.miB,
    BB13_Upr: exports.DefaultNtCClassColors.miB,
    BB13_Lwr: exports.DefaultNtCClassColors.miB,
    BB14_Upr: exports.DefaultNtCClassColors.miB,
    BB14_Lwr: exports.DefaultNtCClassColors.miB,
    BB15_Upr: exports.DefaultNtCClassColors.miB,
    BB15_Lwr: exports.DefaultNtCClassColors.miB,
    BB20_Upr: exports.DefaultNtCClassColors.miB,
    BB20_Lwr: exports.DefaultNtCClassColors.miB,
    IC01_Upr: exports.DefaultNtCClassColors.IC,
    IC01_Lwr: exports.DefaultNtCClassColors.IC,
    IC02_Upr: exports.DefaultNtCClassColors.IC,
    IC02_Lwr: exports.DefaultNtCClassColors.IC,
    IC03_Upr: exports.DefaultNtCClassColors.IC,
    IC03_Lwr: exports.DefaultNtCClassColors.IC,
    IC04_Upr: exports.DefaultNtCClassColors.IC,
    IC04_Lwr: exports.DefaultNtCClassColors.IC,
    IC05_Upr: exports.DefaultNtCClassColors.IC,
    IC05_Lwr: exports.DefaultNtCClassColors.IC,
    IC06_Upr: exports.DefaultNtCClassColors.IC,
    IC06_Lwr: exports.DefaultNtCClassColors.IC,
    IC07_Upr: exports.DefaultNtCClassColors.IC,
    IC07_Lwr: exports.DefaultNtCClassColors.IC,
    OP01_Upr: exports.DefaultNtCClassColors.OPN,
    OP01_Lwr: exports.DefaultNtCClassColors.OPN,
    OP02_Upr: exports.DefaultNtCClassColors.OPN,
    OP02_Lwr: exports.DefaultNtCClassColors.OPN,
    OP03_Upr: exports.DefaultNtCClassColors.OPN,
    OP03_Lwr: exports.DefaultNtCClassColors.OPN,
    OP04_Upr: exports.DefaultNtCClassColors.OPN,
    OP04_Lwr: exports.DefaultNtCClassColors.OPN,
    OP05_Upr: exports.DefaultNtCClassColors.OPN,
    OP05_Lwr: exports.DefaultNtCClassColors.OPN,
    OP06_Upr: exports.DefaultNtCClassColors.OPN,
    OP06_Lwr: exports.DefaultNtCClassColors.OPN,
    OP07_Upr: exports.DefaultNtCClassColors.OPN,
    OP07_Lwr: exports.DefaultNtCClassColors.OPN,
    OP08_Upr: exports.DefaultNtCClassColors.OPN,
    OP08_Lwr: exports.DefaultNtCClassColors.OPN,
    OP09_Upr: exports.DefaultNtCClassColors.OPN,
    OP09_Lwr: exports.DefaultNtCClassColors.OPN,
    OP10_Upr: exports.DefaultNtCClassColors.OPN,
    OP10_Lwr: exports.DefaultNtCClassColors.OPN,
    OP11_Upr: exports.DefaultNtCClassColors.OPN,
    OP11_Lwr: exports.DefaultNtCClassColors.OPN,
    OP12_Upr: exports.DefaultNtCClassColors.OPN,
    OP12_Lwr: exports.DefaultNtCClassColors.OPN,
    OP13_Upr: exports.DefaultNtCClassColors.OPN,
    OP13_Lwr: exports.DefaultNtCClassColors.OPN,
    OP14_Upr: exports.DefaultNtCClassColors.OPN,
    OP14_Lwr: exports.DefaultNtCClassColors.OPN,
    OP15_Upr: exports.DefaultNtCClassColors.OPN,
    OP15_Lwr: exports.DefaultNtCClassColors.OPN,
    OP16_Upr: exports.DefaultNtCClassColors.OPN,
    OP16_Lwr: exports.DefaultNtCClassColors.OPN,
    OP17_Upr: exports.DefaultNtCClassColors.OPN,
    OP17_Lwr: exports.DefaultNtCClassColors.OPN,
    OP18_Upr: exports.DefaultNtCClassColors.OPN,
    OP18_Lwr: exports.DefaultNtCClassColors.OPN,
    OP19_Upr: exports.DefaultNtCClassColors.OPN,
    OP19_Lwr: exports.DefaultNtCClassColors.OPN,
    OP20_Upr: exports.DefaultNtCClassColors.OPN,
    OP20_Lwr: exports.DefaultNtCClassColors.OPN,
    OP21_Upr: exports.DefaultNtCClassColors.OPN,
    OP21_Lwr: exports.DefaultNtCClassColors.OPN,
    OP22_Upr: exports.DefaultNtCClassColors.OPN,
    OP22_Lwr: exports.DefaultNtCClassColors.OPN,
    OP23_Upr: exports.DefaultNtCClassColors.OPN,
    OP23_Lwr: exports.DefaultNtCClassColors.OPN,
    OP24_Upr: exports.DefaultNtCClassColors.OPN,
    OP24_Lwr: exports.DefaultNtCClassColors.OPN,
    OP25_Upr: exports.DefaultNtCClassColors.OPN,
    OP25_Lwr: exports.DefaultNtCClassColors.OPN,
    OP26_Upr: exports.DefaultNtCClassColors.OPN,
    OP26_Lwr: exports.DefaultNtCClassColors.OPN,
    OP27_Upr: exports.DefaultNtCClassColors.OPN,
    OP27_Lwr: exports.DefaultNtCClassColors.OPN,
    OP28_Upr: exports.DefaultNtCClassColors.OPN,
    OP28_Lwr: exports.DefaultNtCClassColors.OPN,
    OP29_Upr: exports.DefaultNtCClassColors.OPN,
    OP29_Lwr: exports.DefaultNtCClassColors.OPN,
    OP30_Upr: exports.DefaultNtCClassColors.OPN,
    OP30_Lwr: exports.DefaultNtCClassColors.OPN,
    OP31_Upr: exports.DefaultNtCClassColors.OPN,
    OP31_Lwr: exports.DefaultNtCClassColors.OPN,
    OPS1_Upr: exports.DefaultNtCClassColors.OPN,
    OPS1_Lwr: exports.DefaultNtCClassColors.OPN,
    OP1S_Upr: exports.DefaultNtCClassColors.OPN,
    OP1S_Lwr: exports.DefaultNtCClassColors.SYN,
    AAS1_Upr: exports.DefaultNtCClassColors.SYN,
    AAS1_Lwr: exports.DefaultNtCClassColors.A,
    AB1S_Upr: exports.DefaultNtCClassColors.A,
    AB1S_Lwr: exports.DefaultNtCClassColors.SYN,
    AB2S_Upr: exports.DefaultNtCClassColors.A,
    AB2S_Lwr: exports.DefaultNtCClassColors.SYN,
    BB1S_Upr: exports.DefaultNtCClassColors.B,
    BB1S_Lwr: exports.DefaultNtCClassColors.SYN,
    BB2S_Upr: exports.DefaultNtCClassColors.B,
    BB2S_Lwr: exports.DefaultNtCClassColors.SYN,
    BBS1_Upr: exports.DefaultNtCClassColors.SYN,
    BBS1_Lwr: exports.DefaultNtCClassColors.B,
    ZZ01_Upr: exports.DefaultNtCClassColors.Z,
    ZZ01_Lwr: exports.DefaultNtCClassColors.Z,
    ZZ02_Upr: exports.DefaultNtCClassColors.Z,
    ZZ02_Lwr: exports.DefaultNtCClassColors.Z,
    ZZ1S_Upr: exports.DefaultNtCClassColors.Z,
    ZZ1S_Lwr: exports.DefaultNtCClassColors.SYN,
    ZZ2S_Upr: exports.DefaultNtCClassColors.Z,
    ZZ2S_Lwr: exports.DefaultNtCClassColors.SYN,
    ZZS1_Upr: exports.DefaultNtCClassColors.SYN,
    ZZS1_Lwr: exports.DefaultNtCClassColors.Z,
    ZZS2_Upr: exports.DefaultNtCClassColors.SYN,
    ZZS2_Lwr: exports.DefaultNtCClassColors.Z,
});
