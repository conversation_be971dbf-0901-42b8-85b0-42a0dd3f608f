"use strict";
/**
 * Copyright (c) 2018-2022 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.NtCTubePreset = void 0;
exports.NtCTubeSegmentLabel = NtCTubeSegmentLabel;
const color_1 = require("./color");
const property_1 = require("./property");
const representation_1 = require("./representation");
const property_2 = require("../property");
const representation_preset_1 = require("../../../mol-plugin-state/builder/structure/representation-preset");
const mol_state_1 = require("../../../mol-state");
const mol_task_1 = require("../../../mol-task");
exports.NtCTubePreset = (0, representation_preset_1.StructureRepresentationPresetProvider)({
    id: 'preset-structure-representation-ntc-tube',
    display: {
        name: 'NtC Tube', group: 'Annotation',
        description: 'NtC Tube',
    },
    isApplicable(a) {
        return a.data.models.length >= 1 && a.data.models.some(m => property_2.Dnatco.isApplicable(m));
    },
    params: () => representation_preset_1.StructureRepresentationPresetProvider.CommonParams,
    async apply(ref, params, plugin) {
        var _a;
        const structureCell = mol_state_1.StateObjectRef.resolveAndCheck(plugin.state.data, ref);
        const model = (_a = structureCell === null || structureCell === void 0 ? void 0 : structureCell.obj) === null || _a === void 0 ? void 0 : _a.data.model;
        if (!structureCell || !model)
            return {};
        await plugin.runTask(mol_task_1.Task.create('NtC tube', async (runtime) => {
            await property_1.NtCTubeProvider.attach({ runtime, assetManager: plugin.managers.asset, errorContext: plugin.errorContext }, model);
        }));
        const { components, representations } = await representation_preset_1.PresetStructureRepresentations.auto.apply(ref, { ...params }, plugin);
        const tube = await plugin.builders.structure.tryCreateComponentStatic(structureCell, 'nucleic', { label: 'NtC Tube' });
        const { update, builder, typeParams } = representation_preset_1.StructureRepresentationPresetProvider.reprBuilder(plugin, params);
        let tubeRepr;
        if (representations)
            tubeRepr = builder.buildRepresentation(update, tube, { type: representation_1.NtCTubeRepresentationProvider, typeParams, color: color_1.NtCTubeColorThemeProvider }, { tag: 'ntc-tube' });
        await update.commit({ revertOnError: true });
        return { components: { ...components, tube }, representations: { ...representations, tubeRepr } };
    }
});
function NtCTubeSegmentLabel(step) {
    return `
        <b>${step.auth_asym_id_1}</b> |
        <b>${step.label_comp_id_1} ${step.auth_seq_id_1}${step.PDB_ins_code_1}${step.label_alt_id_1.length > 0 ? ` (alt ${step.label_alt_id_1})` : ''}
           ${step.label_comp_id_2} ${step.auth_seq_id_2}${step.PDB_ins_code_2}${step.label_alt_id_2.length > 0 ? ` (alt ${step.label_alt_id_2})` : ''} </b><br />
        <i>NtC:</i> ${step.NtC} | <i>Confal score:</i> ${step.confal_score} | <i>RMSD:</i> ${step.rmsd.toFixed(2)}
    `;
}
