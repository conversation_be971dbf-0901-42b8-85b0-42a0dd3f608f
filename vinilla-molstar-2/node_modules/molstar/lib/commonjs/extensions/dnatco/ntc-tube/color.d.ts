/**
 * Copyright (c) 2018-2022 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 */
import { ColorTheme } from '../../../mol-theme/color';
import { ThemeDataContext } from '../../../mol-theme/theme';
import { Color } from '../../../mol-util/color';
import { ParamDefinition as PD } from '../../../mol-util/param-definition';
export declare const NtCTubeColorThemeParams: {
    colors: PD.Mapped<PD.NamedParams<PD.Normalize<unknown>, "default"> | PD.NamedParams<Color, "uniform"> | PD.NamedParams<PD.Normalize<{
        residueMarker: Color;
        stepBoundaryMarker: Color;
        NANT_Upr: Color;
        NANT_Lwr: Color;
        AA00_Upr: Color;
        AA00_Lwr: Color;
        AA02_Upr: Color;
        AA02_Lwr: Color;
        AA03_Upr: Color;
        AA03_Lwr: Color;
        AA04_Upr: Color;
        AA04_Lwr: Color;
        AA08_Upr: Color;
        AA08_Lwr: Color;
        AA09_Upr: Color;
        AA09_Lwr: Color;
        AA01_Upr: Color;
        AA01_Lwr: Color;
        AA05_Upr: Color;
        AA05_Lwr: Color;
        AA06_Upr: Color;
        AA06_Lwr: Color;
        AA10_Upr: Color;
        AA10_Lwr: Color;
        AA11_Upr: Color;
        AA11_Lwr: Color;
        AA07_Upr: Color;
        AA07_Lwr: Color;
        AA12_Upr: Color;
        AA12_Lwr: Color;
        AA13_Upr: Color;
        AA13_Lwr: Color;
        AB01_Upr: Color;
        AB01_Lwr: Color;
        AB02_Upr: Color;
        AB02_Lwr: Color;
        AB03_Upr: Color;
        AB03_Lwr: Color;
        AB04_Upr: Color;
        AB04_Lwr: Color;
        AB05_Upr: Color;
        AB05_Lwr: Color;
        BA01_Upr: Color;
        BA01_Lwr: Color;
        BA05_Upr: Color;
        BA05_Lwr: Color;
        BA09_Upr: Color;
        BA09_Lwr: Color;
        BA08_Upr: Color;
        BA08_Lwr: Color;
        BA10_Upr: Color;
        BA10_Lwr: Color;
        BA13_Upr: Color;
        BA13_Lwr: Color;
        BA16_Upr: Color;
        BA16_Lwr: Color;
        BA17_Upr: Color;
        BA17_Lwr: Color;
        BB00_Upr: Color;
        BB00_Lwr: Color;
        BB01_Upr: Color;
        BB01_Lwr: Color;
        BB17_Upr: Color;
        BB17_Lwr: Color;
        BB02_Upr: Color;
        BB02_Lwr: Color;
        BB03_Upr: Color;
        BB03_Lwr: Color;
        BB11_Upr: Color;
        BB11_Lwr: Color;
        BB16_Upr: Color;
        BB16_Lwr: Color;
        BB04_Upr: Color;
        BB04_Lwr: Color;
        BB05_Upr: Color;
        BB05_Lwr: Color;
        BB07_Upr: Color;
        BB07_Lwr: Color;
        BB08_Upr: Color;
        BB08_Lwr: Color;
        BB10_Upr: Color;
        BB10_Lwr: Color;
        BB12_Upr: Color;
        BB12_Lwr: Color;
        BB13_Upr: Color;
        BB13_Lwr: Color;
        BB14_Upr: Color;
        BB14_Lwr: Color;
        BB15_Upr: Color;
        BB15_Lwr: Color;
        BB20_Upr: Color;
        BB20_Lwr: Color;
        IC01_Upr: Color;
        IC01_Lwr: Color;
        IC02_Upr: Color;
        IC02_Lwr: Color;
        IC03_Upr: Color;
        IC03_Lwr: Color;
        IC04_Upr: Color;
        IC04_Lwr: Color;
        IC05_Upr: Color;
        IC05_Lwr: Color;
        IC06_Upr: Color;
        IC06_Lwr: Color;
        IC07_Upr: Color;
        IC07_Lwr: Color;
        OP01_Upr: Color;
        OP01_Lwr: Color;
        OP02_Upr: Color;
        OP02_Lwr: Color;
        OP03_Upr: Color;
        OP03_Lwr: Color;
        OP04_Upr: Color;
        OP04_Lwr: Color;
        OP05_Upr: Color;
        OP05_Lwr: Color;
        OP06_Upr: Color;
        OP06_Lwr: Color;
        OP07_Upr: Color;
        OP07_Lwr: Color;
        OP08_Upr: Color;
        OP08_Lwr: Color;
        OP09_Upr: Color;
        OP09_Lwr: Color;
        OP10_Upr: Color;
        OP10_Lwr: Color;
        OP11_Upr: Color;
        OP11_Lwr: Color;
        OP12_Upr: Color;
        OP12_Lwr: Color;
        OP13_Upr: Color;
        OP13_Lwr: Color;
        OP14_Upr: Color;
        OP14_Lwr: Color;
        OP15_Upr: Color;
        OP15_Lwr: Color;
        OP16_Upr: Color;
        OP16_Lwr: Color;
        OP17_Upr: Color;
        OP17_Lwr: Color;
        OP18_Upr: Color;
        OP18_Lwr: Color;
        OP19_Upr: Color;
        OP19_Lwr: Color;
        OP20_Upr: Color;
        OP20_Lwr: Color;
        OP21_Upr: Color;
        OP21_Lwr: Color;
        OP22_Upr: Color;
        OP22_Lwr: Color;
        OP23_Upr: Color;
        OP23_Lwr: Color;
        OP24_Upr: Color;
        OP24_Lwr: Color;
        OP25_Upr: Color;
        OP25_Lwr: Color;
        OP26_Upr: Color;
        OP26_Lwr: Color;
        OP27_Upr: Color;
        OP27_Lwr: Color;
        OP28_Upr: Color;
        OP28_Lwr: Color;
        OP29_Upr: Color;
        OP29_Lwr: Color;
        OP30_Upr: Color;
        OP30_Lwr: Color;
        OP31_Upr: Color;
        OP31_Lwr: Color;
        OPS1_Upr: Color;
        OPS1_Lwr: Color;
        OP1S_Upr: Color;
        OP1S_Lwr: Color;
        AAS1_Upr: Color;
        AAS1_Lwr: Color;
        AB1S_Upr: Color;
        AB1S_Lwr: Color;
        AB2S_Upr: Color;
        AB2S_Lwr: Color;
        BB1S_Upr: Color;
        BB1S_Lwr: Color;
        BB2S_Upr: Color;
        BB2S_Lwr: Color;
        BBS1_Upr: Color;
        BBS1_Lwr: Color;
        ZZ01_Upr: Color;
        ZZ01_Lwr: Color;
        ZZ02_Upr: Color;
        ZZ02_Lwr: Color;
        ZZ1S_Upr: Color;
        ZZ1S_Lwr: Color;
        ZZ2S_Upr: Color;
        ZZ2S_Lwr: Color;
        ZZS1_Upr: Color;
        ZZS1_Lwr: Color;
        ZZS2_Upr: Color;
        ZZS2_Lwr: Color;
    }>, "custom">>;
    markResidueBoundaries: PD.BooleanParam;
    markSegmentBoundaries: PD.BooleanParam;
};
export type NtCTubeColorThemeParams = typeof NtCTubeColorThemeParams;
export declare function getNtCTubeColorThemeParams(ctx: ThemeDataContext): {
    colors: PD.Mapped<PD.NamedParams<PD.Normalize<unknown>, "default"> | PD.NamedParams<Color, "uniform"> | PD.NamedParams<PD.Normalize<{
        residueMarker: Color;
        stepBoundaryMarker: Color;
        NANT_Upr: Color;
        NANT_Lwr: Color;
        AA00_Upr: Color;
        AA00_Lwr: Color;
        AA02_Upr: Color;
        AA02_Lwr: Color;
        AA03_Upr: Color;
        AA03_Lwr: Color;
        AA04_Upr: Color;
        AA04_Lwr: Color;
        AA08_Upr: Color;
        AA08_Lwr: Color;
        AA09_Upr: Color;
        AA09_Lwr: Color;
        AA01_Upr: Color;
        AA01_Lwr: Color;
        AA05_Upr: Color;
        AA05_Lwr: Color;
        AA06_Upr: Color;
        AA06_Lwr: Color;
        AA10_Upr: Color;
        AA10_Lwr: Color;
        AA11_Upr: Color;
        AA11_Lwr: Color;
        AA07_Upr: Color;
        AA07_Lwr: Color;
        AA12_Upr: Color;
        AA12_Lwr: Color;
        AA13_Upr: Color;
        AA13_Lwr: Color;
        AB01_Upr: Color;
        AB01_Lwr: Color;
        AB02_Upr: Color;
        AB02_Lwr: Color;
        AB03_Upr: Color;
        AB03_Lwr: Color;
        AB04_Upr: Color;
        AB04_Lwr: Color;
        AB05_Upr: Color;
        AB05_Lwr: Color;
        BA01_Upr: Color;
        BA01_Lwr: Color;
        BA05_Upr: Color;
        BA05_Lwr: Color;
        BA09_Upr: Color;
        BA09_Lwr: Color;
        BA08_Upr: Color;
        BA08_Lwr: Color;
        BA10_Upr: Color;
        BA10_Lwr: Color;
        BA13_Upr: Color;
        BA13_Lwr: Color;
        BA16_Upr: Color;
        BA16_Lwr: Color;
        BA17_Upr: Color;
        BA17_Lwr: Color;
        BB00_Upr: Color;
        BB00_Lwr: Color;
        BB01_Upr: Color;
        BB01_Lwr: Color;
        BB17_Upr: Color;
        BB17_Lwr: Color;
        BB02_Upr: Color;
        BB02_Lwr: Color;
        BB03_Upr: Color;
        BB03_Lwr: Color;
        BB11_Upr: Color;
        BB11_Lwr: Color;
        BB16_Upr: Color;
        BB16_Lwr: Color;
        BB04_Upr: Color;
        BB04_Lwr: Color;
        BB05_Upr: Color;
        BB05_Lwr: Color;
        BB07_Upr: Color;
        BB07_Lwr: Color;
        BB08_Upr: Color;
        BB08_Lwr: Color;
        BB10_Upr: Color;
        BB10_Lwr: Color;
        BB12_Upr: Color;
        BB12_Lwr: Color;
        BB13_Upr: Color;
        BB13_Lwr: Color;
        BB14_Upr: Color;
        BB14_Lwr: Color;
        BB15_Upr: Color;
        BB15_Lwr: Color;
        BB20_Upr: Color;
        BB20_Lwr: Color;
        IC01_Upr: Color;
        IC01_Lwr: Color;
        IC02_Upr: Color;
        IC02_Lwr: Color;
        IC03_Upr: Color;
        IC03_Lwr: Color;
        IC04_Upr: Color;
        IC04_Lwr: Color;
        IC05_Upr: Color;
        IC05_Lwr: Color;
        IC06_Upr: Color;
        IC06_Lwr: Color;
        IC07_Upr: Color;
        IC07_Lwr: Color;
        OP01_Upr: Color;
        OP01_Lwr: Color;
        OP02_Upr: Color;
        OP02_Lwr: Color;
        OP03_Upr: Color;
        OP03_Lwr: Color;
        OP04_Upr: Color;
        OP04_Lwr: Color;
        OP05_Upr: Color;
        OP05_Lwr: Color;
        OP06_Upr: Color;
        OP06_Lwr: Color;
        OP07_Upr: Color;
        OP07_Lwr: Color;
        OP08_Upr: Color;
        OP08_Lwr: Color;
        OP09_Upr: Color;
        OP09_Lwr: Color;
        OP10_Upr: Color;
        OP10_Lwr: Color;
        OP11_Upr: Color;
        OP11_Lwr: Color;
        OP12_Upr: Color;
        OP12_Lwr: Color;
        OP13_Upr: Color;
        OP13_Lwr: Color;
        OP14_Upr: Color;
        OP14_Lwr: Color;
        OP15_Upr: Color;
        OP15_Lwr: Color;
        OP16_Upr: Color;
        OP16_Lwr: Color;
        OP17_Upr: Color;
        OP17_Lwr: Color;
        OP18_Upr: Color;
        OP18_Lwr: Color;
        OP19_Upr: Color;
        OP19_Lwr: Color;
        OP20_Upr: Color;
        OP20_Lwr: Color;
        OP21_Upr: Color;
        OP21_Lwr: Color;
        OP22_Upr: Color;
        OP22_Lwr: Color;
        OP23_Upr: Color;
        OP23_Lwr: Color;
        OP24_Upr: Color;
        OP24_Lwr: Color;
        OP25_Upr: Color;
        OP25_Lwr: Color;
        OP26_Upr: Color;
        OP26_Lwr: Color;
        OP27_Upr: Color;
        OP27_Lwr: Color;
        OP28_Upr: Color;
        OP28_Lwr: Color;
        OP29_Upr: Color;
        OP29_Lwr: Color;
        OP30_Upr: Color;
        OP30_Lwr: Color;
        OP31_Upr: Color;
        OP31_Lwr: Color;
        OPS1_Upr: Color;
        OPS1_Lwr: Color;
        OP1S_Upr: Color;
        OP1S_Lwr: Color;
        AAS1_Upr: Color;
        AAS1_Lwr: Color;
        AB1S_Upr: Color;
        AB1S_Lwr: Color;
        AB2S_Upr: Color;
        AB2S_Lwr: Color;
        BB1S_Upr: Color;
        BB1S_Lwr: Color;
        BB2S_Upr: Color;
        BB2S_Lwr: Color;
        BBS1_Upr: Color;
        BBS1_Lwr: Color;
        ZZ01_Upr: Color;
        ZZ01_Lwr: Color;
        ZZ02_Upr: Color;
        ZZ02_Lwr: Color;
        ZZ1S_Upr: Color;
        ZZ1S_Lwr: Color;
        ZZ2S_Upr: Color;
        ZZ2S_Lwr: Color;
        ZZS1_Upr: Color;
        ZZS1_Lwr: Color;
        ZZS2_Upr: Color;
        ZZS2_Lwr: Color;
    }>, "custom">>;
    markResidueBoundaries: PD.BooleanParam;
    markSegmentBoundaries: PD.BooleanParam;
};
export declare function NtCTubeColorTheme(ctx: ThemeDataContext, props: PD.Values<NtCTubeColorThemeParams>): ColorTheme<NtCTubeColorThemeParams>;
export declare const NtCTubeColorThemeProvider: ColorTheme.Provider<NtCTubeColorThemeParams, 'ntc-tube'>;
