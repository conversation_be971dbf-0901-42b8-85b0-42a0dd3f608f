"use strict";
/**
 * Copyright (c) 2018-2022 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.NtCTubeColorThemeProvider = exports.NtCTubeColorThemeParams = void 0;
exports.getNtCTubeColorThemeParams = getNtCTubeColorThemeParams;
exports.NtCTubeColorTheme = NtCTubeColorTheme;
const color_1 = require("../color");
const property_1 = require("./property");
const types_1 = require("./types");
const property_2 = require("../property");
const color_2 = require("../../../mol-util/color");
const params_1 = require("../../../mol-util/color/params");
const param_definition_1 = require("../../../mol-util/param-definition");
const legend_1 = require("../../../mol-util/legend");
const type_helpers_1 = require("../../../mol-util/type-helpers");
const categories_1 = require("../../../mol-theme/color/categories");
const Description = 'Assigns colors to NtC Tube segments';
const NtCTubeColors = (0, color_2.ColorMap)({
    ...color_1.NtCColors,
    residueMarker: (0, color_2.Color)(0x222222),
    stepBoundaryMarker: (0, color_2.Color)(0x656565),
});
exports.NtCTubeColorThemeParams = {
    colors: param_definition_1.ParamDefinition.MappedStatic('default', {
        'default': param_definition_1.ParamDefinition.EmptyGroup(),
        'custom': param_definition_1.ParamDefinition.Group((0, params_1.getColorMapParams)(NtCTubeColors)),
        'uniform': param_definition_1.ParamDefinition.Color((0, color_2.Color)(0xEEEEEE)),
    }),
    markResidueBoundaries: param_definition_1.ParamDefinition.Boolean(true),
    markSegmentBoundaries: param_definition_1.ParamDefinition.Boolean(true),
};
function getNtCTubeColorThemeParams(ctx) {
    return param_definition_1.ParamDefinition.clone(exports.NtCTubeColorThemeParams);
}
function NtCTubeColorTheme(ctx, props) {
    const colorMap = props.colors.name === 'default'
        ? NtCTubeColors
        : props.colors.name === 'custom'
            ? props.colors.params
            : (0, color_2.ColorMap)({
                ...Object.fromEntries((0, type_helpers_1.ObjectKeys)(NtCTubeColors).map(item => [item, props.colors.params])),
                residueMarker: NtCTubeColors.residueMarker,
                stepBoundaryMarker: NtCTubeColors.stepBoundaryMarker
            });
    function color(location, isSecondary) {
        var _a;
        if (types_1.NtCTubeTypes.isLocation(location)) {
            const { data } = location;
            const { step, kind } = data;
            let key;
            if (kind === 'upper')
                key = step.NtC + '_Upr';
            else if (kind === 'lower')
                key = step.NtC + '_Lwr';
            else if (kind === 'residue-boundary')
                key = (!props.markResidueBoundaries ? step.NtC + '_Lwr' : 'residueMarker');
            else /* segment-boundary */
                key = (!props.markSegmentBoundaries ? step.NtC + '_Lwr' : 'stepBoundaryMarker');
            return (_a = colorMap[key]) !== null && _a !== void 0 ? _a : color_1.ErrorColor;
        }
        return color_1.ErrorColor;
    }
    return {
        factory: NtCTubeColorTheme,
        granularity: 'group',
        color,
        props,
        description: Description,
        legend: (0, legend_1.TableLegend)((0, type_helpers_1.ObjectKeys)(colorMap).map(k => [k.replace('_', ' '), colorMap[k]]).concat([['Error', color_1.ErrorColor]])),
    };
}
exports.NtCTubeColorThemeProvider = {
    name: 'ntc-tube',
    label: 'NtC Tube',
    category: categories_1.ColorThemeCategory.Residue,
    factory: NtCTubeColorTheme,
    getParams: getNtCTubeColorThemeParams,
    defaultValues: param_definition_1.ParamDefinition.getDefaultValues(exports.NtCTubeColorThemeParams),
    isApplicable: (ctx) => !!ctx.structure && ctx.structure.models.some(m => property_2.Dnatco.isApplicable(m)),
    ensureCustomProperties: {
        attach: (ctx, data) => data.structure ? property_1.NtCTubeProvider.attach(ctx, data.structure.models[0], void 0, true) : Promise.resolve(),
        detach: (data) => data.structure && property_1.NtCTubeProvider.ref(data.structure.models[0], false)
    }
};
