"use strict";
/**
 * Copyright (c) 2018-2022 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DnatcoTypes = void 0;
var DnatcoTypes;
(function (DnatcoTypes) {
    DnatcoTypes.DataTag = 'dnatco-confal-half-step';
})(DnatcoTypes || (exports.DnatcoTypes = DnatcoTypes = {}));
