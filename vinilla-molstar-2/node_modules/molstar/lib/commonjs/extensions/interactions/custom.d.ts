/**
 * Copyright (c) 2025 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
import { Structure } from '../../mol-model/structure';
import { InteractionElementSchema, StructureInteractions } from './model';
export declare function getCustomInteractionData(interactions: InteractionElementSchema[], structures: {
    [ref: string]: Structure;
}): StructureInteractions;
