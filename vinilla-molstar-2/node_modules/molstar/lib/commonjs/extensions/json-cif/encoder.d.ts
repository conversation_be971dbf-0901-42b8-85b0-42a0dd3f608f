/**
 * Copyright (c) 2025 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
import { Category, Encoder } from '../../mol-io/writer/cif/encoder';
import { BinaryEncodingProvider } from '../../mol-io/writer/cif/encoder/binary';
import { Writer } from '../../mol-io/writer/writer';
import { JSONCifFile } from './model';
export declare class JSONCifEncoder implements Encoder<string> {
    options?: {
        formatJSON?: boolean;
    } | undefined;
    private data;
    private dataBlocks;
    private encodedData;
    private filter;
    readonly isBinary = false;
    readonly binaryEncodingProvider: BinaryEncodingProvider | undefined;
    setFilter(filter?: Category.Filter): void;
    isCategoryIncluded(name: string): boolean;
    setFormatter(formatter?: Category.Formatter): void;
    startDataBlock(header: string): void;
    writeCategory<Ctx>(category: Category<Ctx>, context?: Ctx, options?: Encoder.WriteCategoryOptions): void;
    encode(): void;
    writeTo(writer: Writer): void;
    getData(): string;
    getSize(): number;
    getFile(): JSONCifFile;
    constructor(encoder: string, options?: {
        formatJSON?: boolean;
    } | undefined);
}
