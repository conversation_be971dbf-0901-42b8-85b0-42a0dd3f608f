"use strict";
/**
 * Copyright (c) 2025 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.JSONCifEncoder = void 0;
const db_1 = require("../../mol-data/db");
const encoder_1 = require("../../mol-io/writer/cif/encoder");
const util_1 = require("../../mol-io/writer/cif/encoder/util");
const model_1 = require("./model");
class JSONCifEncoder {
    setFilter(filter) {
        this.filter = filter || encoder_1.Category.DefaultFilter;
    }
    isCategoryIncluded(name) {
        return this.filter.includeCategory(name);
    }
    setFormatter(formatter) {
        // No formatter needed for JSON encoding.
    }
    startDataBlock(header) {
        this.dataBlocks.push({
            header: (header || '').replace(/[ \n\t]/g, '').toUpperCase(),
            categoryNames: [],
            categories: {}
        });
    }
    writeCategory(category, context, options) {
        if (this.encodedData) {
            throw new Error('The writer contents have already been encoded, no more writing.');
        }
        if (!this.dataBlocks.length) {
            throw new Error('No data block created.');
        }
        if (!(options === null || options === void 0 ? void 0 : options.ignoreFilter) && !this.filter.includeCategory(category.name))
            return;
        const { instance, rowCount, source } = (0, util_1.getCategoryInstanceData)(category, context);
        if (!rowCount)
            return;
        const fields = (0, util_1.getIncludedFields)(instance);
        if (!fields.length)
            return;
        const rows = [];
        const cat = { name: category.name, fieldNames: fields.map(f => f.name), rows };
        for (const src of source) {
            const d = src.data;
            const keys = src.keys();
            while (keys.hasNext) {
                const row = {};
                const k = keys.move();
                for (const f of fields) {
                    const kind = f.valueKind ? f.valueKind(k, d) : db_1.Column.ValueKinds.Present;
                    if (kind === db_1.Column.ValueKinds.Present) {
                        row[f.name] = f.value(k, d, rows.length);
                    }
                    else if (kind === db_1.Column.ValueKinds.NotPresent) {
                        row[f.name] = null;
                    }
                }
                cat.rows.push(row);
            }
        }
        this.dataBlocks[this.dataBlocks.length - 1].categoryNames.push(cat.name);
        this.dataBlocks[this.dataBlocks.length - 1].categories[cat.name] = cat;
    }
    encode() {
        var _a;
        if (this.encodedData)
            return;
        this.encodedData = ((_a = this.options) === null || _a === void 0 ? void 0 : _a.formatJSON) ? JSON.stringify(this.data, null, 2) : JSON.stringify(this.data);
    }
    writeTo(writer) {
        writer.writeString(this.encodedData);
    }
    getData() {
        this.encode();
        return this.encodedData;
    }
    getSize() {
        var _a, _b;
        return (_b = (_a = this.encodedData) === null || _a === void 0 ? void 0 : _a.length) !== null && _b !== void 0 ? _b : 0;
    }
    getFile() {
        return this.data;
    }
    constructor(encoder, options) {
        this.options = options;
        this.dataBlocks = [];
        this.filter = encoder_1.Category.DefaultFilter;
        this.isBinary = false;
        this.data = {
            encoder,
            version: model_1.JSONCifVERSION,
            dataBlocks: this.dataBlocks
        };
    }
}
exports.JSONCifEncoder = JSONCifEncoder;
