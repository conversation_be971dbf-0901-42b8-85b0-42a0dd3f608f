/**
 * Copyright (c) 2025 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
import { CifFile } from '../../mol-io/reader/cif';
import { ReaderResult } from '../../mol-io/reader/result';
import { Task } from '../../mol-task';
import { JSONCifFile } from './model';
export declare function parseJSONCif(data: JSONCifFile): CifFile;
export declare function parseJSONCifString(data: string): Task<ReaderResult<CifFile>>;
