"use strict";
/**
 * Copyright (c) 2025 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseJSONCif = parseJSONCif;
exports.parseJSONCifString = parseJSONCifString;
const db_1 = require("../../mol-data/db");
const cif_1 = require("../../mol-io/reader/cif");
const result_1 = require("../../mol-io/reader/result");
const mol_task_1 = require("../../mol-task");
function Field(rows, name) {
    const str = row => {
        const v = rows[row][name];
        if (v === null || v === undefined)
            return '';
        if (typeof v === 'string')
            return v;
        return '' + v;
    };
    const number = row => +rows[row][name];
    const valueKind = row => {
        const v = rows[row][name];
        if (v === null)
            return db_1.Column.ValueKinds.NotPresent;
        if (v === undefined)
            return db_1.Column.ValueKinds.Unknown;
        return db_1.Column.ValueKinds.Present;
    };
    const rowCount = rows.length;
    return {
        __array: undefined,
        binaryEncoding: undefined,
        isDefined: true,
        rowCount,
        str,
        int: number,
        float: number,
        valueKind,
        areValuesEqual: (rowA, rowB) => rows[rowA][name] === rows[rowB][name],
        toStringArray: params => db_1.ColumnHelpers.createAndFillArray(rowCount, str, params),
        toIntArray: params => db_1.ColumnHelpers.createAndFillArray(rowCount, number, params),
        toFloatArray: params => db_1.ColumnHelpers.createAndFillArray(rowCount, number, params),
    };
}
function Category(data) {
    const nameSet = new Set(data.fieldNames);
    const cache = Object.create(null);
    return {
        rowCount: data.rows.length,
        name: data.name,
        fieldNames: data.fieldNames,
        getField(name) {
            if (!nameSet.has(name))
                return void 0;
            if (!!cache[name])
                return cache[name];
            cache[name] = Field(data.rows, name);
            return cache[name];
        }
    };
}
function checkVersions(min, current) {
    for (let i = 0; i < 2; i++) {
        if (min[i] > current[i])
            return false;
    }
    return true;
}
function parseJSONCif(data) {
    const minVersion = [0, 1];
    if (!checkVersions(minVersion, data.version.match(/(\d)\.(\d)\.\d/).slice(1).map(v => +v))) {
        throw new Error(`Unsupported format version. Current ${data.version}, required ${minVersion.join('.')}.`);
    }
    return (0, cif_1.CifFile)(data.dataBlocks.map(block => {
        const cats = Object.create(null);
        for (const cat of block.categoryNames)
            cats[cat] = Category(block.categories[cat]);
        return (0, cif_1.CifBlock)(block.categoryNames, cats, block.header);
    }));
}
function parseJSONCifString(data) {
    return mol_task_1.Task.create('Parse BinaryCIF', async (ctx) => {
        try {
            const json = JSON.parse(data);
            const file = parseJSONCif(json);
            return result_1.ReaderResult.success(file);
        }
        catch (e) {
            return result_1.ReaderResult.error('' + e);
        }
    });
}
