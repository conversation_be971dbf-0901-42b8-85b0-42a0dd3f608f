/**
 * Copyright (c) 2025 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
import { PluginStateObject } from '../../mol-plugin-state/objects';
import { StateTransformer } from '../../mol-state';
import { ParamDefinition } from '../../mol-util/param-definition';
import { JSONCifFile } from './model';
export declare const ParseJSONCifFileData: StateTransformer<PluginStateObject.Root, PluginStateObject.Format.Cif, ParamDefinition.Normalize<{
    data: JSONCifFile;
}>>;
