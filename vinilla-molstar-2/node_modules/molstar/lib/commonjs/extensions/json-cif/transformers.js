"use strict";
/**
 * Copyright (c) 2025 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ParseJSONCifFileData = void 0;
const objects_1 = require("../../mol-plugin-state/objects");
const mol_state_1 = require("../../mol-state");
const mol_task_1 = require("../../mol-task");
const param_definition_1 = require("../../mol-util/param-definition");
const parser_1 = require("./parser");
const Transform = mol_state_1.StateTransformer.builderFactory('json-cif');
exports.ParseJSONCifFileData = Transform({
    name: 'parse-json-cif-data',
    from: objects_1.PluginStateObject.Root,
    to: objects_1.PluginStateObject.Format.Cif,
    params: {
        data: param_definition_1.ParamDefinition.Value(undefined, { isHidden: true }),
    }
})({
    apply({ params }) {
        return mol_task_1.Task.create('Parse JSON Cif', async (ctx) => {
            const parsed = (0, parser_1.parseJSONCif)(params.data);
            return new objects_1.PluginStateObject.Format.Cif(parsed, { label: 'CIF Data' });
        });
    }
});
