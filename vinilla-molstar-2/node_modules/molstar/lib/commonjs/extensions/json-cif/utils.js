"use strict";
/**
 * Copyright (c) 2025 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.molfileToJSONCif = molfileToJSONCif;
const parser_1 = require("../../mol-io/reader/mol/parser");
const mol_1 = require("../../mol-model-formats/structure/mol");
const structure_1 = require("../../mol-model/structure");
const mol_task_1 = require("../../mol-task");
const encoder_1 = require("./encoder");
async function molfileToJSONCif(molfile) {
    const parsed = await (0, parser_1.parseMol)(molfile).run();
    if (parsed.isError)
        throw new Error(parsed.message);
    const models = await (0, mol_1.trajectoryFromMol)(parsed.result).run();
    const model = await mol_task_1.Task.resolveInContext(models.getFrameAtIndex(0));
    const structure = structure_1.Structure.ofModel(model);
    const encoder = new encoder_1.JSONCifEncoder('Mol*', { formatJSON: true });
    (0, structure_1.to_mmCIF)('mol', structure, false, {
        encoder,
        includedCategoryNames: new Set(['atom_site']),
        extensions: {
            molstar_bond_site: true,
        }
    });
    return {
        structure,
        molfile: parsed.result,
        jsoncif: encoder.getFile()
    };
}
