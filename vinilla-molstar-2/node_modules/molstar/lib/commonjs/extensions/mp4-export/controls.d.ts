/**
 * Copyright (c) 2020 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
import { PluginStateAnimation } from '../../mol-plugin-state/animation/model';
import { PluginComponent } from '../../mol-plugin-state/component';
import { PluginContext } from '../../mol-plugin/context';
import { ParamDefinition as PD } from '../../mol-util/param-definition';
export interface Mp4AnimationInfo {
    width: number;
    height: number;
}
export declare const Mp4AnimationParams: {
    quantization: PD.Numeric;
};
export declare class Mp4Controls extends PluginComponent {
    private plugin;
    private currentNames;
    private animations;
    readonly behaviors: {
        animations: import("rxjs").BehaviorSubject<PD.Params>;
        current: import("rxjs").BehaviorSubject<{
            anim: PluginStateAnimation;
            params: PD.Params;
            values: any;
        } | undefined>;
        canApply: import("rxjs").BehaviorSubject<PluginStateAnimation.CanApply>;
        info: import("rxjs").BehaviorSubject<Mp4AnimationInfo>;
        params: import("rxjs").BehaviorSubject<PD.Values<{
            quantization: PD.Numeric;
        }>>;
    };
    setCurrent(name?: string): void;
    setCurrentParams(values: any): void;
    get current(): {
        anim: PluginStateAnimation;
        params: PD.Params;
        values: any;
    } | undefined;
    render(): Promise<{
        movie: Uint8Array<ArrayBufferLike>;
        filename: string;
    }>;
    private get manager();
    private syncInfo;
    private sync;
    private init;
    private updateCanApply;
    constructor(plugin: PluginContext);
}
