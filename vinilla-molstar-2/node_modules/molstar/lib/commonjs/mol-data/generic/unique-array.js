"use strict";
/**
 * Copyright (c) 2017 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.UniqueArray = void 0;
var UniqueArray;
(function (UniqueArray) {
    function create() {
        return { keys: new Set(), array: [] };
    }
    UniqueArray.create = create;
    function add({ keys, array }, key, value) {
        if (keys.has(key))
            return false;
        keys.add(key);
        array[array.length] = value;
        return true;
    }
    UniqueArray.add = add;
    function has({ keys }, key) {
        return keys.has(key);
    }
    UniqueArray.has = has;
})(UniqueArray || (exports.UniqueArray = UniqueArray = {}));
