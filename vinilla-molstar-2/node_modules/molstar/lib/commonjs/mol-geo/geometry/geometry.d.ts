/**
 * Copyright (c) 2018-2025 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
import { Mesh } from './mesh/mesh';
import { Points } from './points/points';
import { Text } from './text/text';
import { RenderableState } from '../../mol-gl/renderable';
import { LocationIterator } from '../util/location-iterator';
import { ColorType } from './color-data';
import { SizeType } from './size-data';
import { Lines } from './lines/lines';
import { ParamDefinition as PD } from '../../mol-util/param-definition';
import { DirectVolume } from './direct-volume/direct-volume';
import { Color } from '../../mol-util/color';
import { Spheres } from './spheres/spheres';
import { TransformData } from './transform-data';
import { Theme } from '../../mol-theme/theme';
import { RenderObjectValues } from '../../mol-gl/render-object';
import { TextureMesh } from './texture-mesh/texture-mesh';
import { Image } from './image/image';
import { Cylinders } from './cylinders/cylinders';
export type GeometryKind = 'mesh' | 'points' | 'spheres' | 'cylinders' | 'text' | 'lines' | 'direct-volume' | 'image' | 'texture-mesh';
export type Geometry<T extends GeometryKind = GeometryKind> = T extends 'mesh' ? Mesh : T extends 'points' ? Points : T extends 'spheres' ? Spheres : T extends 'cylinders' ? Cylinders : T extends 'text' ? Text : T extends 'lines' ? Lines : T extends 'direct-volume' ? DirectVolume : T extends 'image' ? Image : T extends 'texture-mesh' ? TextureMesh : never;
type GeometryParams<T extends GeometryKind> = T extends 'mesh' ? Mesh.Params : T extends 'points' ? Points.Params : T extends 'spheres' ? Spheres.Params : T extends 'cylinders' ? Cylinders.Params : T extends 'text' ? Text.Params : T extends 'lines' ? Lines.Params : T extends 'direct-volume' ? DirectVolume.Params : T extends 'image' ? Image.Params : T extends 'texture-mesh' ? TextureMesh.Params : never;
export interface GeometryUtils<G extends Geometry, P extends PD.Params = GeometryParams<G['kind']>, V = RenderObjectValues<G['kind']>> {
    Params: P;
    createEmpty(geometry?: G): G;
    createValues(geometry: G, transform: TransformData, locationIt: LocationIterator, theme: Theme, props: PD.Values<P>): V;
    createValuesSimple(geometry: G, props: Partial<PD.Values<P>>, colorValue: Color, sizeValue: number, transform?: TransformData): V;
    updateValues(values: V, props: PD.Values<P>): void;
    updateBoundingSphere(values: V, geometry: G): void;
    createRenderableState(props: PD.Values<P>): RenderableState;
    updateRenderableState(state: RenderableState, props: PD.Values<P>): void;
    createPositionIterator(geometry: G, transform: TransformData): LocationIterator;
}
export declare namespace Geometry {
    type Params<G extends Geometry> = GeometryParams<G['kind']>;
    function getDrawCount(geometry: Geometry): number;
    function getVertexCount(geometry: Geometry): number;
    function getGroupCount(geometry: Geometry): number;
    function getUtils<G extends Geometry>(geometry: G): GeometryUtils<G>;
    function getGranularity<T extends ColorType | SizeType>(locationIt: LocationIterator, granularity: T): "group" | T;
}
export {};
