"use strict";
/**
 * Copyright (c) 2018-2025 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Geometry = void 0;
const mesh_1 = require("./mesh/mesh");
const points_1 = require("./points/points");
const text_1 = require("./text/text");
const lines_1 = require("./lines/lines");
const direct_volume_1 = require("./direct-volume/direct-volume");
const spheres_1 = require("./spheres/spheres");
const array_1 = require("../../mol-util/array");
const texture_mesh_1 = require("./texture-mesh/texture-mesh");
const image_1 = require("./image/image");
const cylinders_1 = require("./cylinders/cylinders");
const number_packing_1 = require("../../mol-util/number-packing");
var Geometry;
(function (Geometry) {
    function getDrawCount(geometry) {
        switch (geometry.kind) {
            case 'mesh': return geometry.triangleCount * 3;
            case 'points': return geometry.pointCount;
            case 'spheres': return geometry.sphereCount * 2 * 3;
            case 'cylinders': return geometry.cylinderCount * 4 * 3;
            case 'text': return geometry.charCount * 2 * 3;
            case 'lines': return geometry.lineCount * 2 * 3;
            case 'direct-volume': return 12 * 3;
            case 'image': return 2 * 3;
            case 'texture-mesh': return geometry.vertexCount;
        }
    }
    Geometry.getDrawCount = getDrawCount;
    function getVertexCount(geometry) {
        switch (geometry.kind) {
            case 'mesh': return geometry.vertexCount;
            case 'points': return geometry.pointCount;
            case 'spheres': return geometry.sphereCount * 6;
            case 'cylinders': return geometry.cylinderCount * 6;
            case 'text': return geometry.charCount * 4;
            case 'lines': return geometry.lineCount * 4;
            case 'direct-volume':
                const [x, y, z] = geometry.gridDimension.ref.value;
                return x * y * z;
            case 'image': return 4;
            case 'texture-mesh': return geometry.vertexCount;
        }
    }
    Geometry.getVertexCount = getVertexCount;
    function getGroupCount(geometry) {
        switch (geometry.kind) {
            case 'mesh':
            case 'points':
            case 'spheres':
            case 'cylinders':
            case 'text':
            case 'lines':
                return getDrawCount(geometry) === 0 ? 0 : ((0, array_1.arrayMax)(geometry.groupBuffer.ref.value) + 1);
            case 'direct-volume':
                return 1;
            case 'image':
                return (0, number_packing_1.arrayMaxPackedIntToRGB)(geometry.groupTexture.ref.value.array, 4) + 1;
            case 'texture-mesh':
                return geometry.groupCount;
        }
    }
    Geometry.getGroupCount = getGroupCount;
    function getUtils(geometry) {
        // TODO avoid casting
        switch (geometry.kind) {
            case 'mesh': return mesh_1.Mesh.Utils;
            case 'points': return points_1.Points.Utils;
            case 'spheres': return spheres_1.Spheres.Utils;
            case 'cylinders': return cylinders_1.Cylinders.Utils;
            case 'text': return text_1.Text.Utils;
            case 'lines': return lines_1.Lines.Utils;
            case 'direct-volume': return direct_volume_1.DirectVolume.Utils;
            case 'image': return image_1.Image.Utils;
            case 'texture-mesh': return texture_mesh_1.TextureMesh.Utils;
        }
    }
    Geometry.getUtils = getUtils;
    function getGranularity(locationIt, granularity) {
        return granularity === 'instance' && locationIt.nonInstanceable ? 'group' : granularity;
    }
    Geometry.getGranularity = getGranularity;
})(Geometry || (exports.Geometry = Geometry = {}));
