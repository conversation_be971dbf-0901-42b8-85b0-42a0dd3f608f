export declare const assign_material_color = "\n#if defined(dNeedsMarker)\n    float marker = uMarker;\n    if (uMarker == -1.0) {\n        marker = floor(vMarker * 255.0 + 0.5); // rounding required to work on some cards on win\n    }\n#endif\n\n#if defined(dRenderVariant_color) || defined(dRenderVariant_tracing)\n    #if defined(dUsePalette)\n        vec4 material = vec4(texture2D(tPalette, vec2(vPaletteV, 0.5)).rgb, uAlpha);\n    #elif defined(dColorType_uniform)\n        vec4 material = vec4(uColor, uAlpha);\n    #elif defined(dColorType_varying)\n        vec4 material = vec4(vColor.rgb, uAlpha);\n    #endif\n\n    // mix material with overpaint\n    #if defined(dOverpaint)\n        material.rgb = mix(material.rgb, vOverpaint.rgb, vOverpaint.a);\n    #endif\n\n    float emissive = uEmissive;\n    #ifdef dEmissive\n        emissive += vEmissive;\n    #endif\n\n    float metalness = uMetalness;\n    float roughness = uRoughness;\n    float bumpiness = uBumpiness;\n    #ifdef dSubstance\n        float sf = clamp(vSubstance.a, 0.0, 0.99); // clamp to avoid artifacts\n        metalness = mix(metalness, vSubstance.r, sf);\n        roughness = mix(roughness, vSubstance.g, sf);\n        bumpiness = mix(bumpiness, vSubstance.b, sf);\n    #endif\n\n    #if defined(dXrayShaded)\n        material.a = calcXrayShadedAlpha(material.a, normal);\n    #endif\n#elif defined(dRenderVariant_depth)\n    if (fragmentDepth > getDepth(gl_FragCoord.xy / uDrawingBufferSize)) {\n        discard;\n    }\n    vec4 material;\n    if (uRenderMask == MaskOpaque) {\n        #if defined(dXrayShaded)\n            discard;\n        #endif\n        #if defined(dTransparency)\n            float dta = 1.0 - vTransparency;\n            #if __VERSION__ == 100 || defined(dVaryingGroup)\n                if (vTransparency < 0.1) dta = 1.0; // hard cutoff to avoid artifacts\n            #endif\n\n            if (uAlpha * dta < 1.0) {\n                discard;\n            }\n        #else\n            if (uAlpha < 1.0) {\n                discard;\n            }\n        #endif\n        material = packDepthToRGBA(fragmentDepth);\n    } else if (uRenderMask == MaskTransparent) {\n        float alpha = uAlpha;\n        #if defined(dTransparency)\n            float dta = 1.0 - vTransparency;\n            alpha *= dta;\n        #endif\n\n        #ifdef dXrayShaded\n            alpha = calcXrayShadedAlpha(alpha, normal);\n        #else\n            if (alpha == 1.0) {\n                discard;\n            }\n        #endif\n        material = packDepthWithAlphaToRGBA(fragmentDepth, alpha);\n    }\n#elif defined(dRenderVariant_marking)\n    vec4 material;\n    if(uMarkingType == 1) {\n        if (marker > 0.0)\n            discard;\n        #ifdef enabledFragDepth\n            material = packDepthToRGBA(gl_FragDepthEXT);\n        #else\n            material = packDepthToRGBA(gl_FragCoord.z);\n        #endif\n    } else {\n        if (marker == 0.0)\n            discard;\n        float depthTest = 1.0;\n        if (uMarkingDepthTest) {\n            depthTest = (fragmentDepth >= getDepthPacked(gl_FragCoord.xy / uDrawingBufferSize)) ? 1.0 : 0.0;\n        }\n        bool isHighlight = intMod(marker, 2.0) > 0.1;\n        float viewZ = depthToViewZ(uIsOrtho, fragmentDepth, uNear, uFar);\n        float fogFactor = smoothstep(uFogNear, uFogFar, abs(viewZ));\n        if (fogFactor == 1.0)\n            discard;\n        material = vec4(0.0, depthTest, isHighlight ? 1.0 : 0.0, 1.0 - fogFactor);\n    }\n#elif defined(dRenderVariant_emissive)\n    float emissive = uEmissive;\n    #ifdef dEmissive\n        emissive += vEmissive;\n    #endif\n    vec4 material = vec4(emissive);\n#endif\n\n// apply per-group transparency\n#if defined(dTransparency) && (defined(dRenderVariant_pick) || defined(dRenderVariant_color) || defined(dRenderVariant_emissive) || defined(dRenderVariant_tracing))\n    float ta = 1.0 - vTransparency;\n    if (vTransparency < 0.09) ta = 1.0; // hard cutoff looks better\n\n    #if defined(dRenderVariant_pick)\n        if (ta * uAlpha < uPickingAlphaThreshold)\n            discard; // ignore so the element below can be picked\n    #elif defined(dRenderVariant_emissive)\n        if (ta < 1.0)\n            discard; // emissive not supported with transparency\n    #elif defined(dRenderVariant_color) || defined(dRenderVariant_tracing)\n        material.a *= ta;\n    #endif\n#endif\n";
