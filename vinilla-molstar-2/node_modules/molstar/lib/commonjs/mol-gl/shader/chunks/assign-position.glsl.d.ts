export declare const assign_position = "\nmat4 model = uModel * aTransform;\nmat4 modelView = uView * model;\n#ifdef dGeometryType_textureMesh\n    vec3 position = readFromTexture(tPosition, vertexId, uGeoTexDim).xyz;\n#else\n    vec3 position = aPosition;\n#endif\nvec4 position4 = vec4(position, 1.0);\n// for accessing tColorGrid in vert shader and for clipping in frag shader\nvModelPosition = (model * position4).xyz;\nvec4 mvPosition = modelView * position4;\nvViewPosition = mvPosition.xyz;\ngl_Position = uProjection * mvPosition;\n";
