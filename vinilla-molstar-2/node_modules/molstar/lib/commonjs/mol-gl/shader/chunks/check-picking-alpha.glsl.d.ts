export declare const check_picking_alpha = "\nfloat viewZ = depthToViewZ(uIsOrtho, fragmentDepth, uNear, uFar);\nfloat fogFactor = smoothstep(uFogNear, uFogFar, abs(viewZ));\nfloat fogAlpha = (1.0 - fogFactor) * uAlpha;\nfloat alpha = uAlpha;\n#ifdef dXrayShaded\n    // add bias to make picking xray shaded elements easier\n    alpha = calcXrayShadedAlpha(alpha, normal) + (0.3 * uPickingAlphaThreshold);\n#endif\n// if not opaque enough ignore so the element below can be picked\nif (alpha < uPickingAlphaThreshold || fogAlpha < 0.1) {\n    #ifdef dTransparentBackfaces_opaque\n        if (!interior) discard;\n    #else\n        discard;\n    #endif\n}\n";
