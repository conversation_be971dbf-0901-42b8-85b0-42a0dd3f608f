export declare const color_frag_params = "\nuniform float uMetalness;\nuniform float uRoughness;\nuniform float uBumpiness;\n#ifdef bumpEnabled\n    uniform float uBumpFrequency;\n    uniform float uBumpAmplitude;\n#endif\nuniform float uEmissive;\n\n// Density value to estimate object thickness\nuniform float uDensity;\n\n#if defined(dRenderVariant_color) || defined(dRenderVariant_tracing)\n    #if defined(dColorType_uniform)\n        uniform vec3 uColor;\n    #elif defined(dColorType_varying)\n        varying vec4 vColor;\n    #endif\n\n    #ifdef dUsePalette\n        uniform sampler2D tPalette;\n        varying float vPaletteV;\n    #endif\n\n    #ifdef dOverpaint\n        varying vec4 vOverpaint;\n    #endif\n\n    #ifdef dEmissive\n        varying float vEmissive;\n    #endif\n\n    #ifdef dSubstance\n        varying vec4 vSubstance;\n    #endif\n#elif defined(dRenderVariant_emissive)\n    #ifdef dEmissive\n        varying float vEmissive;\n    #endif\n#elif defined(dRenderVariant_pick)\n    #if __VERSION__ == 100 || !defined(dVaryingGroup)\n        #ifdef requiredDrawBuffers\n            varying vec4 vObject;\n            varying vec4 vInstance;\n            varying vec4 vGroup;\n        #else\n            varying vec4 vColor;\n        #endif\n    #else\n        #ifdef requiredDrawBuffers\n            flat in vec4 vObject;\n            flat in vec4 vInstance;\n            flat in vec4 vGroup;\n        #else\n            flat in vec4 vColor;\n        #endif\n    #endif\n#endif\n\n#ifdef dTransparency\n    varying float vTransparency;\n#endif\n";
