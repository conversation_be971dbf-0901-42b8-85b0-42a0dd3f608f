export declare const common = "\n// TODO find a better place for these convenience defines\n\n#if defined(dRenderVariant_colorBlended) || defined(dRenderVariant_colorWboit) || defined(dRenderVariant_colorDpoit)\n    #define dRenderVariant_color\n#endif\n\n#if defined(dColorType_instance) || defined(dColorType_group) || defined(dColorType_groupInstance) || defined(dColorType_vertex) || defined(dColorType_vertexInstance)\n    #define dColorType_texture\n#endif\n\n#if defined(dColorType_volume) || defined(dColorType_volumeInstance)\n    #define dColorType_grid\n#endif\n\n#if defined(dColorType_attribute) || defined(dColorType_texture) || defined(dColorType_grid)\n    #define dColorType_varying\n#endif\n\n#if ((defined(dRenderVariant_color) || defined(dRenderVariant_tracing)) && defined(dColorMarker)) || defined(dRenderVariant_marking)\n    #define dNeedsMarker\n#endif\n\n#if defined(dXrayShaded_on) || defined(dXrayShaded_inverted)\n    #define dXrayShaded\n#endif\n\n#if defined(dRenderVariant_color) || defined(dRenderVariant_tracing) || ((defined(dRenderVariant_depth) || defined(dRenderVariant_pick)) && defined(dXrayShaded))\n    #define dNeedsNormal\n#endif\n\n#define MaskAll 0\n#define MaskOpaque 1\n#define MaskTransparent 2\n\n//\n\n#define PI 3.14159265\n#define RECIPROCAL_PI 0.31830988618\n#define EPSILON 1e-6\n#define ONE_MINUS_EPSILON 1.0 - EPSILON\n#define TWO_PI 6.2831853\n#define HALF_PI 1.570796325\n\n#define PALETTE_SCALE 16777214.0 // (1 << 24) - 2\n\n#define saturate(a) clamp(a, 0.0, 1.0)\n\n#if __VERSION__ == 100\n    #define round(x) floor((x) + 0.5)\n#endif\n\nfloat intDiv(const in float a, const in float b) { return float(int(a) / int(b)); }\nvec2 ivec2Div(const in vec2 a, const in vec2 b) { return vec2(ivec2(a) / ivec2(b)); }\nfloat intMod(const in float a, const in float b) { return a - b * float(int(a) / int(b)); }\nint imod(const in int a, const in int b) { return a - b * (a / b); }\n\nfloat pow2(const in float x) { return x * x; }\n\nvec3 packIntToRGB(in float value) {\n    value = clamp(round(value), 0.0, 16777216.0 - 1.0) + 1.0;\n    vec3 c = vec3(0.0);\n    c.b = mod(value, 256.0);\n    value = floor(value / 256.0);\n    c.g = mod(value, 256.0);\n    value = floor(value / 256.0);\n    c.r = mod(value, 256.0);\n    return c / 255.0;\n}\nfloat unpackRGBToInt(const in vec3 rgb) {\n    return (floor(rgb.r * 255.0 + 0.5) * 256.0 * 256.0 + floor(rgb.g * 255.0 + 0.5) * 256.0 + floor(rgb.b * 255.0 + 0.5)) - 1.0;\n}\n\nvec2 packUnitIntervalToRG(const in float v) {\n    vec2 enc;\n    enc.xy = vec2(fract(v * 256.0), v);\n    enc.y -= enc.x * (1.0 / 256.0);\n    enc.xy *=  256.0 / 255.0;\n\n    return enc;\n}\n\nfloat unpackRGToUnitInterval(const in vec2 enc) {\n    return dot(enc, vec2(255.0 / (256.0 * 256.0), 255.0 / 256.0));\n}\n\nvec3 screenSpaceToViewSpace(const in vec3 ssPos, const in mat4 invProjection) {\n    vec4 p = vec4(ssPos * 2.0 - 1.0, 1.0);\n    p = invProjection * p;\n    return p.xyz / p.w;\n}\n\nconst float PackUpscale = 256.0 / 255.0; // fraction -> 0..1 (including 1)\nconst float UnpackDownscale = 255.0 / 256.0; // 0..1 -> fraction (excluding 1)\nconst vec3 PackFactors = vec3(256.0 * 256.0 * 256.0, 256.0 * 256.0,  256.0);\nconst vec4 UnpackFactors = UnpackDownscale / vec4(PackFactors, 1.0);\nconst float ShiftRight8 = 1.0 / 256.0;\n\nvec4 packDepthToRGBA(const in float v) {\n    vec4 r = vec4(fract(v * PackFactors), v);\n    r.yzw -= r.xyz * ShiftRight8; // tidy overflow\n    return r * PackUpscale;\n}\nfloat unpackRGBAToDepth(const in vec4 v) {\n    return dot(v, UnpackFactors);\n}\n\nvec4 packDepthWithAlphaToRGBA(const in float depth, const in float alpha){\n    vec3 r = vec3(fract(depth * PackFactors.yz), depth);\n    r.yz -= r.xy * ShiftRight8; // tidy overflow\n    return vec4(r * PackUpscale, alpha);\n}\nvec2 unpackRGBAToDepthWithAlpha(const in vec4 v) {\n    return vec2(dot(v.xyz, UnpackFactors.yzw), v.w);\n}\n\nvec4 sRGBToLinear(const in vec4 c) {\n    return vec4(mix(pow(c.rgb * 0.9478672986 + vec3(0.0521327014), vec3(2.4)), c.rgb * 0.0773993808, vec3(lessThanEqual(c.rgb, vec3(0.04045)))), c.a);\n}\nvec4 linearTosRGB(const in vec4 c) {\n    return vec4(mix(pow(c.rgb, vec3(0.41666)) * 1.055 - vec3(0.055), c.rgb * 12.92, vec3(lessThanEqual(c.rgb, vec3(0.0031308)))), c.a);\n}\n\nfloat luminance(vec3 c) {\n    // https://www.w3.org/TR/WCAG21/#dfn-relative-luminance\n    const vec3 W = vec3(0.2125, 0.7154, 0.0721);\n    return dot(c, W);\n}\n\nfloat linearizeDepth(const in float depth, const in float near, const in float far) {\n    return (2.0 * near) / (far + near - depth * (far - near));\n}\n\nfloat perspectiveDepthToViewZ(const in float invClipZ, const in float near, const in float far) {\n    return (near * far) / ((far - near) * invClipZ - far);\n}\n\nfloat orthographicDepthToViewZ(const in float linearClipZ, const in float near, const in float far) {\n    return linearClipZ * (near - far) - near;\n}\n\nfloat depthToViewZ(const in float isOrtho, const in float linearClipZ, const in float near, const in float far) {\n    return isOrtho == 1.0 ? orthographicDepthToViewZ(linearClipZ, near, far) : perspectiveDepthToViewZ(linearClipZ, near, far);\n}\n\n// see https://github.com/graphitemaster/normals_revisited and https://www.shadertoy.com/view/3s33zj\nmat3 adjoint(const in mat4 m) {\n    return mat3(\n        cross(m[1].xyz, m[2].xyz),\n        cross(m[2].xyz, m[0].xyz),\n        cross(m[0].xyz, m[1].xyz)\n    );\n}\n\n#if __VERSION__ == 100\n    // transpose\n\n    float transpose(const in float m) {\n        return m;\n    }\n\n    mat2 transpose2(const in mat2 m) {\n        return mat2(\n            m[0][0], m[1][0],\n            m[0][1], m[1][1]\n        );\n    }\n\n    mat3 transpose3(const in mat3 m) {\n        return mat3(\n            m[0][0], m[1][0], m[2][0],\n            m[0][1], m[1][1], m[2][1],\n            m[0][2], m[1][2], m[2][2]\n        );\n    }\n\n    mat4 transpose4(const in mat4 m) {\n        return mat4(\n            m[0][0], m[1][0], m[2][0], m[3][0],\n            m[0][1], m[1][1], m[2][1], m[3][1],\n            m[0][2], m[1][2], m[2][2], m[3][2],\n            m[0][3], m[1][3], m[2][3], m[3][3]\n        );\n    }\n\n    // inverse\n\n    float inverse(const in float m) {\n        return 1.0 / m;\n    }\n\n    mat2 inverse2(const in mat2 m) {\n        return mat2(m[1][1],-m[0][1],\n                -m[1][0], m[0][0]) / (m[0][0]*m[1][1] - m[0][1]*m[1][0]);\n    }\n\n    mat3 inverse3(const in mat3 m) {\n        float a00 = m[0][0], a01 = m[0][1], a02 = m[0][2];\n        float a10 = m[1][0], a11 = m[1][1], a12 = m[1][2];\n        float a20 = m[2][0], a21 = m[2][1], a22 = m[2][2];\n\n        float b01 = a22 * a11 - a12 * a21;\n        float b11 = -a22 * a10 + a12 * a20;\n        float b21 = a21 * a10 - a11 * a20;\n\n        float det = a00 * b01 + a01 * b11 + a02 * b21;\n\n        return mat3(b01, (-a22 * a01 + a02 * a21), (a12 * a01 - a02 * a11),\n                    b11, (a22 * a00 - a02 * a20), (-a12 * a00 + a02 * a10),\n                    b21, (-a21 * a00 + a01 * a20), (a11 * a00 - a01 * a10)) / det;\n    }\n\n    mat4 inverse4(const in mat4 m) {\n        float\n            a00 = m[0][0], a01 = m[0][1], a02 = m[0][2], a03 = m[0][3],\n            a10 = m[1][0], a11 = m[1][1], a12 = m[1][2], a13 = m[1][3],\n            a20 = m[2][0], a21 = m[2][1], a22 = m[2][2], a23 = m[2][3],\n            a30 = m[3][0], a31 = m[3][1], a32 = m[3][2], a33 = m[3][3],\n\n            b00 = a00 * a11 - a01 * a10,\n            b01 = a00 * a12 - a02 * a10,\n            b02 = a00 * a13 - a03 * a10,\n            b03 = a01 * a12 - a02 * a11,\n            b04 = a01 * a13 - a03 * a11,\n            b05 = a02 * a13 - a03 * a12,\n            b06 = a20 * a31 - a21 * a30,\n            b07 = a20 * a32 - a22 * a30,\n            b08 = a20 * a33 - a23 * a30,\n            b09 = a21 * a32 - a22 * a31,\n            b10 = a21 * a33 - a23 * a31,\n            b11 = a22 * a33 - a23 * a32,\n\n            det = b00 * b11 - b01 * b10 + b02 * b09 + b03 * b08 - b04 * b07 + b05 * b06;\n\n        return mat4(\n            a11 * b11 - a12 * b10 + a13 * b09,\n            a02 * b10 - a01 * b11 - a03 * b09,\n            a31 * b05 - a32 * b04 + a33 * b03,\n            a22 * b04 - a21 * b05 - a23 * b03,\n            a12 * b08 - a10 * b11 - a13 * b07,\n            a00 * b11 - a02 * b08 + a03 * b07,\n            a32 * b02 - a30 * b05 - a33 * b01,\n            a20 * b05 - a22 * b02 + a23 * b01,\n            a10 * b10 - a11 * b08 + a13 * b06,\n            a01 * b08 - a00 * b10 - a03 * b06,\n            a30 * b04 - a31 * b02 + a33 * b00,\n            a21 * b02 - a20 * b04 - a23 * b00,\n            a11 * b07 - a10 * b09 - a12 * b06,\n            a00 * b09 - a01 * b07 + a02 * b06,\n            a31 * b01 - a30 * b03 - a32 * b00,\n            a20 * b03 - a21 * b01 + a22 * b00) / det;\n    }\n\n    #define isNaN(x) ((x) != (x))\n    #define isInf(x) ((x) == (x) + 1.0)\n#else\n    #define transpose2(m) transpose(m)\n    #define transpose3(m) transpose(m)\n    #define transpose4(m) transpose(m)\n\n    #define inverse2(m) inverse(m)\n    #define inverse3(m) inverse(m)\n    #define inverse4(m) inverse(m)\n\n    #define isNaN isnan\n    #define isInf isinf\n#endif\n";
