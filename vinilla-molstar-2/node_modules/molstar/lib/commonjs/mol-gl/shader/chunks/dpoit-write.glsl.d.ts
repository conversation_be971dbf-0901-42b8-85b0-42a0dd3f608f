/**
 * Copyright (c) 2022-2023 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
export declare const dpoit_write = "\n#if defined(dRenderVariant_colorDpoit)\n    if (uRenderMask == MaskOpaque) {\n        if (preFogAlpha < 1.0) {\n            discard;\n        }\n    } else if (uRenderMask == MaskTransparent) {\n        vec2 coords = gl_FragCoord.xy / uDrawingBufferSize;\n        if (preFogAlpha != 1.0 && fragmentDepth < getDepth(coords)) {\n            #ifdef dTransparentBackfaces_off\n                if (interior) discard;\n            #endif\n\n            // adapted from https://github.com/tsherif/webgl2examples\n            // The MIT License, Copyright 2017 Tarek Sherif, Shuai Shao\n\n            vec2 lastDepth = texture2D(tDpoitDepth, coords).rg;\n            vec4 lastFrontColor = texture2D(tDpoitFrontColor, coords);\n\n            vec4 fragColor = gl_FragColor;\n\n            // depth value always increases\n            // so we can use MAX blend equation\n            gl_FragData[2].rg = vec2(-MAX_DPOIT_DEPTH);\n\n            // front color always increases\n            // so we can use MAX blend equation\n            gl_FragColor = lastFrontColor;\n\n            // back color is separately blend afterwards each pass\n            gl_FragData[1] = vec4(0.0);\n\n            float nearestDepth = -lastDepth.x;\n            float furthestDepth = lastDepth.y;\n            float alphaMultiplier = 1.0 - lastFrontColor.a;\n\n            if (fragmentDepth < nearestDepth || fragmentDepth > furthestDepth) {\n                // Skip this depth since it's been peeled.\n                return;\n            }\n\n            if (fragmentDepth > nearestDepth && fragmentDepth < furthestDepth) {\n                // This needs to be peeled.\n                // The ones remaining after MAX blended for\n                // all need-to-peel will be peeled next pass.\n                gl_FragData[2].rg = vec2(-fragmentDepth, fragmentDepth);\n                return;\n            }\n\n            // write to back and front color buffer\n            if (fragmentDepth == nearestDepth) {\n                gl_FragColor.rgb += fragColor.rgb * fragColor.a * alphaMultiplier;\n                gl_FragColor.a = 1.0 - alphaMultiplier * (1.0 - fragColor.a);\n            } else {\n                gl_FragData[1] += fragColor;\n            }\n\n        } else {\n            discard;\n        }\n    }\n#endif\n";
