/**
 * Copyright (c) 2023-2024 mol* contributors, licensed under MIT, See LICENSE file for more info.
 *
 * <AUTHOR> <<EMAIL>>
 */
export declare const fade_lod = "\nif (uLod.w == 0.0 && (uLod.x != 0.0 || uLod.y != 0.0)) {\n    float d = dot(uCameraPlane.xyz, vModelPosition) + uCameraPlane.w;\n    float ta = min(\n        smoothstep(uLod.x, uLod.x + uLod.z, d),\n        1.0 - smoothstep(uLod.y - uLod.z, uLod.y, d)\n    );\n\n    #if defined(dRenderVariant_color) || defined(dRenderVariant_tracing)\n        float at = 0.0;\n\n        // shift by view-offset during multi-sample rendering to allow for blending\n        vec2 coord = gl_FragCoord.xy + uViewOffset * 0.25;\n\n        const mat4 thresholdMatrix = mat4(\n            1.0 / 17.0,  9.0 / 17.0,  3.0 / 17.0, 11.0 / 17.0,\n            13.0 / 17.0,  5.0 / 17.0, 15.0 / 17.0,  7.0 / 17.0,\n            4.0 / 17.0, 12.0 / 17.0,  2.0 / 17.0, 10.0 / 17.0,\n            16.0 / 17.0,  8.0 / 17.0, 14.0 / 17.0,  6.0 / 17.0\n        );\n        int ci = int(intMod(coord.x, 4.0));\n        int ri = int(intMod(coord.y, 4.0));\n        #if __VERSION__ == 100\n            vec4 i = vec4(float(ci * 4 + ri));\n            vec4 v = thresholdMatrix[0] * vec4(equal(i, vec4(0.0, 1.0, 2.0, 3.0))) +\n                thresholdMatrix[1] * vec4(equal(i, vec4(4.0, 5.0, 6.0, 7.0))) +\n                thresholdMatrix[2] * vec4(equal(i, vec4(8.0, 9.0, 10.0, 11.0))) +\n                thresholdMatrix[3] * vec4(equal(i, vec4(12.0, 13.0, 14.0, 15.0)));\n            at = v.x + v.y + v.z + v.w;\n        #else\n            at = thresholdMatrix[ci][ri];\n        #endif\n\n        if (ta < 0.99 && (ta < 0.01 || ta < at)) {\n            discard;\n        }\n    #else\n        if (ta < uPickingAlphaThreshold) {\n            discard;\n        }\n    #endif\n}\n";
