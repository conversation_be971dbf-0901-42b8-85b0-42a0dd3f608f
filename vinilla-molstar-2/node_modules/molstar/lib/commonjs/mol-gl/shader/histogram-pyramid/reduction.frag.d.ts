export declare const reduction_frag = "\nprecision highp float;\nprecision highp int;\nprecision highp sampler2D;\n\nuniform sampler2D tInputLevel;\n\n// previous level used to evaluate the new level\n#if __VERSION__ == 100\n    uniform sampler2D tPreviousLevel;\n#else\n    precision highp isampler2D;\n    uniform isampler2D tPreviousLevel;\n#endif\n\n// inverted size of the previous level texture.\nuniform float uSize;\nuniform float uTexSize;\nuniform bool uFirst;\n\n#include common\n\nvoid main(void) {\n    float k = 0.5 * uSize;\n    vec2 position = floor((gl_FragCoord.xy / uTexSize) / uSize) * uSize;\n\n    #if __VERSION__ == 100\n        float a, b, c, d;\n\n        if (uFirst) {\n            a = texture2D(tInputLevel, position).r * 255.0;\n            b = texture2D(tInputLevel, position + vec2(k, 0.0)).r * 255.0;\n            c = texture2D(tInputLevel, position + vec2(0.0, k)).r * 255.0;\n            d = texture2D(tInputLevel, position + vec2(k, k)).r * 255.0;\n        } else {\n            a = unpackRGBToInt(texture2D(tPreviousLevel, position).rgb);\n            b = unpackRGBToInt(texture2D(tPreviousLevel, position + vec2(k, 0.0)).rgb);\n            c = unpackRGBToInt(texture2D(tPreviousLevel, position + vec2(0.0, k)).rgb);\n            d = unpackRGBToInt(texture2D(tPreviousLevel, position + vec2(k, k)).rgb);\n        }\n        gl_FragColor = vec4(packIntToRGB(a + b + c + d), 1.0);\n    #else\n        int a, b, c, d;\n\n        if (uFirst) {\n            a = int(texture2D(tInputLevel, position).r * 255.0);\n            b = int(texture2D(tInputLevel, position + vec2(k, 0.0)).r * 255.0);\n            c = int(texture2D(tInputLevel, position + vec2(0.0, k)).r * 255.0);\n            d = int(texture2D(tInputLevel, position + vec2(k, k)).r * 255.0);\n        } else {\n            a = texture2D(tPreviousLevel, position).r;\n            b = texture2D(tPreviousLevel, position + vec2(k, 0.0)).r;\n            c = texture2D(tPreviousLevel, position + vec2(0.0, k)).r;\n            d = texture2D(tPreviousLevel, position + vec2(k, k)).r;\n        }\n        gl_FragColor = ivec4(a + b + c + d);\n    #endif\n}\n";
