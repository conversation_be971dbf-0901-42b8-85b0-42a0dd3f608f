export declare const overlay_frag = "\nprecision highp float;\nprecision highp sampler2D;\n\nuniform vec2 uTexSizeInv;\nuniform sampler2D tEdgeTexture;\nuniform vec3 uHighlightEdgeColor;\nuniform vec3 uSelectEdgeColor;\nuniform float uHighlightEdgeStrength;\nuniform float uSelectEdgeStrength;\nuniform float uGhostEdgeStrength;\nuniform float uInnerEdgeFactor;\n\nvoid main() {\n    vec2 coords = gl_FragCoord.xy * uTexSizeInv;\n    vec4 edgeValue = texture2D(tEdgeTexture, coords);\n    if (edgeValue.a > 0.0) {\n        vec3 edgeColor = edgeValue.b == 1.0 ? uHighlightEdgeColor : uSelectEdgeColor;\n        gl_FragColor.rgb = edgeValue.g > 0.0 ? edgeColor : edgeColor * uInnerEdgeFactor;\n        gl_FragColor.a = (edgeValue.r == 1.0 ? uGhostEdgeStrength : 1.0) * edgeValue.a;\n        float edgeStrength = edgeValue.b == 1.0 ? uHighlightEdgeStrength : uSelectEdgeStrength;\n        gl_FragColor.a *= edgeStrength;\n    } else {\n        gl_FragColor = vec4(0.0);\n    }\n}\n";
