/**
 * Slightly adapted from https://github.com/mrdoob/three.js
 * MIT License Copyright (c) 2010-2020 three.js authors
 *
 * WebGL port of Subpixel Morphological Antialiasing (SMAA) v2.8
 * Preset: SMAA 1x Medium (with color edge detection)
 * https://github.com/iryoku/smaa/releases/tag/v2.8
 */
export declare const weights_frag = "\nprecision highp float;\nprecision highp int;\nprecision highp sampler2D;\n\n#define SMAASampleLevelZeroOffset(tex, coord, offset) texture2D(tex, coord + float(offset) * uTexSizeInv, 0.0)\n\n#define SMAA_AREATEX_MAX_DISTANCE 16\n#define SMAA_AREATEX_PIXEL_SIZE (1.0 / vec2(160.0, 560.0))\n#define SMAA_AREATEX_SUBTEX_SIZE (1.0 / 7.0)\n\nuniform sampler2D tEdges;\nuniform sampler2D tArea;\nuniform sampler2D tSearch;\nuniform vec2 uTexSizeInv;\n\nvarying vec2 vUv;\nvarying vec4 vOffset[3];\nvarying vec2 vPixCoord;\n\n#if __VERSION__ == 100\n    vec2 round(vec2 x) {\n        return sign(x) * floor(abs(x) + 0.5);\n    }\n#endif\n\nfloat SMAASearchLength(sampler2D searchTex, vec2 e, float bias, float scale) {\n    // Not required if searchTex accesses are set to point:\n    // float2 SEARCH_TEX_PIXEL_SIZE = 1.0 / float2(66.0, 33.0);\n    // e = float2(bias, 0.0) + 0.5 * SEARCH_TEX_PIXEL_SIZE +\n    //     e * float2(scale, 1.0) * float2(64.0, 32.0) * SEARCH_TEX_PIXEL_SIZE;\n    e.r = bias + e.r * scale;\n    return 255.0 * texture2D(searchTex, e, 0.0).r;\n}\n\nfloat SMAASearchXLeft(sampler2D edgesTex, sampler2D searchTex, vec2 texCoord, float end) {\n    /**\n     * @PSEUDO_GATHER4\n     * This texCoord has been offset by (-0.25, -0.125) in the vertex shader to\n     * sample between edge, thus fetching four edges in a row.\n     * Sampling with different offsets in each direction allows to disambiguate\n     * which edges are active from the four fetched ones.\n     */\n    vec2 e = vec2(0.0, 1.0);\n\n    for (int i = 0; i < dMaxSearchSteps; i++) { // WebGL port note: Changed while to for\n        e = texture2D( edgesTex, texCoord, 0.0).rg;\n        texCoord -= vec2(2.0, 0.0) * uTexSizeInv;\n        if (!(texCoord.x > end && e.g > 0.8281 && e.r == 0.0)) break;\n    }\n\n    // We correct the previous (-0.25, -0.125) offset we applied:\n    texCoord.x += 0.25 * uTexSizeInv.x;\n\n    // The searches are bias by 1, so adjust the coords accordingly:\n    texCoord.x += uTexSizeInv.x;\n\n    // Disambiguate the length added by the last step:\n    texCoord.x += 2.0 * uTexSizeInv.x; // Undo last step\n    texCoord.x -= uTexSizeInv.x * SMAASearchLength(searchTex, e, 0.0, 0.5);\n\n    return texCoord.x;\n}\n\nfloat SMAASearchXRight(sampler2D edgesTex, sampler2D searchTex, vec2 texCoord, float end) {\n    vec2 e = vec2( 0.0, 1.0 );\n\n    for (int i = 0; i < dMaxSearchSteps; i++) { // WebGL port note: Changed while to for\n        e = texture2D(edgesTex, texCoord, 0.0).rg;\n        texCoord += vec2(2.0, 0.0) * uTexSizeInv;\n        if (!(texCoord.x < end && e.g > 0.8281 && e.r == 0.0)) break;\n    }\n\n    texCoord.x -= 0.25 * uTexSizeInv.x;\n    texCoord.x -= uTexSizeInv.x;\n    texCoord.x -= 2.0 * uTexSizeInv.x;\n    texCoord.x += uTexSizeInv.x * SMAASearchLength( searchTex, e, 0.5, 0.5 );\n\n    return texCoord.x;\n}\n\nfloat SMAASearchYUp(sampler2D edgesTex, sampler2D searchTex, vec2 texCoord, float end) {\n    vec2 e = vec2( 1.0, 0.0 );\n\n    for (int i = 0; i < dMaxSearchSteps; i++) { // WebGL port note: Changed while to for\n        e = texture2D(edgesTex, texCoord, 0.0).rg;\n        texCoord += vec2(0.0, 2.0) * uTexSizeInv; // WebGL port note: Changed sign\n        if (!(texCoord.y > end && e.r > 0.8281 && e.g == 0.0)) break;\n    }\n\n    texCoord.y -= 0.25 * uTexSizeInv.y; // WebGL port note: Changed sign\n    texCoord.y -= uTexSizeInv.y; // WebGL port note: Changed sign\n    texCoord.y -= 2.0 * uTexSizeInv.y; // WebGL port note: Changed sign\n    texCoord.y += uTexSizeInv.y * SMAASearchLength(searchTex, e.gr, 0.0, 0.5); // WebGL port note: Changed sign\n\n    return texCoord.y;\n}\n\nfloat SMAASearchYDown(sampler2D edgesTex, sampler2D searchTex, vec2 texCoord, float end) {\n    vec2 e = vec2( 1.0, 0.0 );\n\n    for (int i = 0; i < dMaxSearchSteps; i++) { // WebGL port note: Changed while to for\n        e = texture2D(edgesTex, texCoord, 0.0).rg;\n        texCoord -= vec2( 0.0, 2.0 ) * uTexSizeInv; // WebGL port note: Changed sign\n        if (!(texCoord.y < end && e.r > 0.8281 && e.g == 0.0)) break;\n    }\n\n    texCoord.y += 0.25 * uTexSizeInv.y; // WebGL port note: Changed sign\n    texCoord.y += uTexSizeInv.y; // WebGL port note: Changed sign\n    texCoord.y += 2.0 * uTexSizeInv.y; // WebGL port note: Changed sign\n    texCoord.y -= uTexSizeInv.y * SMAASearchLength(searchTex, e.gr, 0.5, 0.5); // WebGL port note: Changed sign\n\n    return texCoord.y;\n}\n\nvec2 SMAAArea(sampler2D areaTex, vec2 dist, float e1, float e2, float offset) {\n    // Rounding prevents precision errors of bilinear filtering:\n    vec2 texCoord = float(SMAA_AREATEX_MAX_DISTANCE) * round(4.0 * vec2(e1, e2)) + dist;\n\n    // We do a scale and bias for mapping to texel space:\n    texCoord = SMAA_AREATEX_PIXEL_SIZE * texCoord + (0.5 * SMAA_AREATEX_PIXEL_SIZE);\n\n    // Move to proper place, according to the subpixel offset:\n    texCoord.y += SMAA_AREATEX_SUBTEX_SIZE * offset;\n\n    return texture2D(areaTex, texCoord, 0.0).rg;\n}\n\nvec4 SMAABlendingWeightCalculationPS(vec2 texCoord, vec2 pixCoord, vec4 offset[3], sampler2D edgesTex, sampler2D areaTex, sampler2D searchTex, ivec4 subsampleIndices) {\n    vec4 weights = vec4(0.0, 0.0, 0.0, 0.0);\n\n    vec2 e = texture2D(edgesTex, texCoord).rg;\n\n    if (e.g > 0.0) { // Edge at north\n        vec2 d;\n\n        // Find the distance to the left:\n        vec2 coords;\n        coords.x = SMAASearchXLeft(edgesTex, searchTex, offset[0].xy, offset[2].x );\n        coords.y = offset[1].y; // offset[1].y = texCoord.y - 0.25 * uTexSizeInv.y (@CROSSING_OFFSET)\n        d.x = coords.x;\n\n        // Now fetch the left crossing edges, two at a time using bilinear\n        // filtering. Sampling at -0.25 (see @CROSSING_OFFSET) enables to\n        // discern what value each edge has:\n        float e1 = texture2D(edgesTex, coords, 0.0).r;\n\n        // Find the distance to the right:\n        coords.x = SMAASearchXRight(edgesTex, searchTex, offset[0].zw, offset[2].y);\n        d.y = coords.x;\n\n        // We want the distances to be in pixel units (doing this here allow to\n        // better interleave arithmetic and memory accesses):\n        d = d / uTexSizeInv.x - pixCoord.x;\n\n        // SMAAArea below needs a sqrt, as the areas texture is compressed\n        // quadratically:\n        vec2 sqrt_d = sqrt(abs(d));\n\n        // Fetch the right crossing edges:\n        coords.y -= 1.0 * uTexSizeInv.y; // WebGL port note: Added\n        float e2 = SMAASampleLevelZeroOffset(edgesTex, coords, ivec2(1, 0)).r;\n\n        // Ok, we know how this pattern looks like, now it is time for getting\n        // the actual area:\n        weights.rg = SMAAArea(areaTex, sqrt_d, e1, e2, float(subsampleIndices.y));\n    }\n\n    if (e.r > 0.0) { // Edge at west\n        vec2 d;\n\n        // Find the distance to the top:\n        vec2 coords;\n\n        coords.y = SMAASearchYUp(edgesTex, searchTex, offset[1].xy, offset[2].z );\n        coords.x = offset[0].x; // offset[1].x = texCoord.x - 0.25 * uTexSizeInv.x;\n        d.x = coords.y;\n\n        // Fetch the top crossing edges:\n        float e1 = texture2D(edgesTex, coords, 0.0).g;\n\n        // Find the distance to the bottom:\n        coords.y = SMAASearchYDown(edgesTex, searchTex, offset[1].zw, offset[2].w);\n        d.y = coords.y;\n\n        // We want the distances to be in pixel units:\n        d = d / uTexSizeInv.y - pixCoord.y;\n\n        // SMAAArea below needs a sqrt, as the areas texture is compressed\n        // quadratically:\n        vec2 sqrt_d = sqrt(abs(d));\n\n        // Fetch the bottom crossing edges:\n        coords.y -= 1.0 * uTexSizeInv.y; // WebGL port note: Added\n        float e2 = SMAASampleLevelZeroOffset(edgesTex, coords, ivec2(0, 1)).g;\n\n        // Get the area for this direction:\n        weights.ba = SMAAArea(areaTex, sqrt_d, e1, e2, float(subsampleIndices.x));\n    }\n\n    return weights;\n}\n\nvoid main() {\n    gl_FragColor = SMAABlendingWeightCalculationPS(vUv, vPixCoord, vOffset, tEdges, tArea, tSearch, ivec4(0.0));\n}\n";
