{"name": "molstar", "version": "4.18.0", "description": "A comprehensive macromolecular library.", "homepage": "https://github.com/molstar/molstar#readme", "repository": {"type": "git", "url": "https://github.com/molstar/molstar.git"}, "bugs": {"url": "https://github.com/molstar/molstar/issues"}, "engines": {"node": ">=18.0.0"}, "scripts": {"lint": "eslint .", "lint-fix": "eslint . --fix", "test": "npm install --no-save \"gl@^6.0.2\" && npm run lint && jest", "jest": "jest", "build": "npm run build-tsc && npm run build-extra && npm run build-webpack", "clean": "node ./scripts/clean.js", "rebuild": "npm run clean && npm run build", "build-viewer": "npm run build-tsc && npm run build-extra && npm run build-webpack-viewer", "build-tsc": "concurrently \"tsc --incremental\" \"tsc --build tsconfig.commonjs.json --incremental\"", "build-extra": "cpx \"src/**/*.{scss,html,ico,jpg}\" lib/", "build-webpack": "webpack --mode production --config ./webpack.config.production.js", "build-webpack-viewer": "webpack --mode production --config ./webpack.config.viewer.js", "watch": "concurrently -c \"green,green,gray,gray\" --names \"tsc,srv,ext,wpc\" --kill-others \"npm:watch-tsc\" \"npm:watch-servers\" \"npm:watch-extra\" \"npm:watch-webpack\"", "watch-viewer": "concurrently -c \"green,gray,gray\" --names \"tsc,ext,wpc\" --kill-others \"npm:watch-tsc\" \"npm:watch-extra\" \"npm:watch-webpack-viewer\"", "watch-viewer-debug": "concurrently -c \"green,gray,gray\" --names \"tsc,ext,wpc\" --kill-others \"npm:watch-tsc\" \"npm:watch-extra\" \"npm:watch-webpack-viewer-debug\"", "watch-tsc": "tsc --watch --incremental", "watch-servers": "tsc --build tsconfig.commonjs.json --watch --incremental", "watch-extra": "cpx \"src/**/*.{scss,html,ico,jpg}\" lib/ --watch", "watch-webpack": "webpack -w --mode development --stats minimal", "watch-webpack-viewer": "webpack -w --mode development --stats minimal --config ./webpack.config.viewer.js", "watch-webpack-viewer-debug": "webpack -w --mode development --stats minimal --config ./webpack.config.viewer.debug.js", "dev": "node build-dev.mjs", "dev:all": "node build-dev.mjs -a -e", "dev:viewer": "node build-dev.mjs -a viewer", "dev:apps": "node build-dev.mjs -a", "dev:examples": "node build-dev.mjs -e", "serve": "http-server -p 1338 -g", "model-server": "node lib/commonjs/servers/model/server.js", "model-server-watch": "nodemon --watch lib lib/commonjs/servers/model/server.js", "volume-server-test": "node lib/commonjs/servers/volume/server.js --idMap em 'test/${id}.mdb' --defaultPort 1336", "plugin-state": "node lib/commonjs/servers/plugin-state/index.js --working-folder ./build/state --port 1339", "preversion": "npm run test", "version": "npm run rebuild && cpx .npmignore lib/", "postversion": "git push && git push --tags"}, "files": ["lib/", "build/viewer/", "build/mvs-stories/"], "bin": {"cif2bcif": "lib/commonjs/cli/cif2bcif/index.js", "cifschema": "lib/commonjs/cli/cifschema/index.js", "mvs-validate": "lib/commonjs/cli/mvs/mvs-validate.js", "mvs-render": "lib/commonjs/cli/mvs/mvs-render.js", "mvs-print-schema": "lib/commonjs/cli/mvs/mvs-print-schema.js", "model-server": "lib/commonjs/servers/model/server.js", "model-server-query": "lib/commonjs/servers/model/query.js", "model-server-preprocess": "lib/commonjs/servers/model/preprocess.js", "volume-server": "lib/commonjs/servers/volume/server.js", "volume-server-query": "lib/commonjs/servers/volume/query.js", "volume-server-pack": "lib/commonjs/servers/volume/pack.js"}, "nodemonConfig": {"ignoreRoot": ["./node_modules", ".git"], "ignore": [], "delay": "2500"}, "jest": {"moduleFileExtensions": ["ts", "js"], "transform": {"\\.ts$": "ts-jest"}, "moduleDirectories": ["node_modules", "lib"], "testEnvironmentOptions": {"url": "http://localhost/"}, "testRegex": "\\.spec\\.ts$"}, "author": "Mol* Contributors", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Sime<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>"], "license": "MIT", "devDependencies": {"@types/cors": "^2.8.19", "@types/gl": "^6.0.5", "@types/jest": "^29.5.14", "@types/pngjs": "^6.0.5", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "benchmark": "^2.1.4", "concurrently": "^9.1.2", "cpx2": "^8.0.0", "crypto-browserify": "^3.12.1", "css-loader": "^7.1.2", "esbuild": "^0.25.5", "esbuild-sass-plugin": "^3.3.1", "eslint": "^8.57.1", "extra-watch-webpack-plugin": "^1.0.3", "file-loader": "^6.2.0", "fs-extra": "^11.3.0", "http-server": "^14.1.1", "jest": "^29.7.0", "jpeg-js": "^0.4.4", "mini-css-extract-plugin": "^2.9.2", "path-browserify": "^1.0.1", "raw-loader": "^4.0.2", "react": "^18.3.1", "react-dom": "^18.3.1", "sass": "^1.89.1", "sass-loader": "^16.0.5", "simple-git": "^3.28.0", "stream-browserify": "^3.0.0", "style-loader": "^4.0.0", "ts-jest": "^29.3.4", "typescript": "^5.8.3", "webpack": "^5.99.9", "webpack-cli": "^6.0.1"}, "dependencies": {"@types/argparse": "^2.0.17", "@types/benchmark": "^2.1.5", "@types/compression": "1.8.1", "@types/express": "^5.0.3", "@types/node": "^18.19.111", "@types/node-fetch": "^2.6.12", "@types/swagger-ui-dist": "3.30.5", "argparse": "^2.0.1", "compression": "^1.8.0", "cors": "^2.8.5", "express": "^5.1.0", "h264-mp4-encoder": "^1.0.12", "immer": "^10.1.1", "immutable": "^5.1.2", "io-ts": "^2.2.22", "node-fetch": "^2.7.0", "react-markdown": "^10.1.0", "rxjs": "^7.8.2", "swagger-ui-dist": "^5.24.0", "tslib": "^2.8.1", "util.promisify": "^1.1.3"}, "peerDependencies": {"@google-cloud/storage": "^7.14.0", "canvas": "^2.11.2", "gl": "^6.0.2", "jpeg-js": "^0.4.4", "pngjs": "^6.0.0", "react": ">=16.14.0", "react-dom": ">=16.14.0"}, "peerDependenciesMeta": {"@google-cloud/storage": {"optional": true}, "canvas": {"optional": true}, "gl": {"optional": true}, "jpeg-js": {"optional": true}, "pngjs": {"optional": true}}}