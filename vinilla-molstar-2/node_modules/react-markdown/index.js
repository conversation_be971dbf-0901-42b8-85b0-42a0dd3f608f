/**
 * @typedef {import('./lib/index.js').AllowElement} AllowElement
 * @typedef {import('./lib/index.js').Components} Components
 * @typedef {import('./lib/index.js').ExtraProps} ExtraProps
 * @typedef {import('./lib/index.js').HooksOptions} HooksOptions
 * @typedef {import('./lib/index.js').Options} Options
 * @typedef {import('./lib/index.js').UrlTransform} UrlTransform
 */

export {
  MarkdownAsync,
  MarkdownHooks,
  Markdown as default,
  defaultUrlTransform
} from './lib/index.js'
