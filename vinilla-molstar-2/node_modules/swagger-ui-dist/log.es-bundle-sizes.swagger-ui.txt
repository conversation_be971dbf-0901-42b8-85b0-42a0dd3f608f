ramda: 367.12 KB (5.46%)
lodash: 255.69 KB (3.80%)
ramda-adjunct: 255.04 KB (3.79%)
autolinker: 203.32 KB (3.02%)
@swagger-api/apidom-ns-openapi-3-0: 192.35 KB (2.86%)
swagger-client: 181.4 KB (2.70%)
immutable: 139.01 KB (2.07%)
@swagger-api/apidom-ns-openapi-3-1: 133.71 KB (1.99%)
react-dom: 129.93 KB (1.93%)
remarkable: 125.56 KB (1.87%)
highlight.js: 111.85 KB (1.66%)
js-yaml: 105.01 KB (1.56%)
readable-stream: 96.66 KB (1.44%)
@swagger-api/apidom-reference: 84.47 KB (1.26%)
core-js-pure: 83.37 KB (1.24%)
dompurify: 60.6 KB (0.901%)
minim: 57.35 KB (0.853%)
buffer: 56.99 KB (0.848%)
@swagger-api/apidom-core: 54.82 KB (0.815%)
@swagger-api/apidom-ast: 52.36 KB (0.779%)
@swagger-api/apidom-ns-json-schema-draft-4: 52.02 KB (0.774%)
react-syntax-highlighter: 40.21 KB (0.598%)
short-unique-id: 37.48 KB (0.557%)
apg-lite: 37.34 KB (0.555%)
react-redux: 36.62 KB (0.545%)
@swaggerexpert/cookie: 35.16 KB (0.523%)
@swagger-api/apidom-ns-json-schema-2019-09: 34.97 KB (0.520%)
fast-json-patch: 31.89 KB (0.474%)
@swaggerexpert/json-pointer: 30.48 KB (0.453%)
@swagger-api/apidom-ns-json-schema-draft-7: 23.54 KB (0.350%)
@swagger-api/apidom-ns-json-schema-2020-12: 22.33 KB (0.332%)
@swagger-api/apidom-ns-json-schema-draft-6: 21.56 KB (0.321%)
reselect: 21.47 KB (0.319%)
openapi-path-templating: 20.81 KB (0.309%)
neotraverse: 20.33 KB (0.302%)
sha.js: 18.92 KB (0.281%)
ts-mixer: 17.56 KB (0.261%)
tslib: 17.23 KB (0.256%)
redux: 16.37 KB (0.244%)
url-parse: 16.23 KB (0.241%)
events: 14.54 KB (0.216%)
openapi-server-url-templating: 14.38 KB (0.214%)
get-intrinsic: 14.02 KB (0.209%)
zenscroll: 12.31 KB (0.183%)
react-debounce-input: 11.95 KB (0.178%)
react-immutable-proptypes: 11.82 KB (0.176%)
ret: 10.82 KB (0.161%)
lodash.debounce: 10.53 KB (0.157%)
unraw: 9.9 KB (0.147%)
string_decoder: 9.24 KB (0.137%)
xml: 7.39 KB (0.110%)
react-copy-to-clipboard: 7.33 KB (0.109%)
react: 6.95 KB (0.103%)
randexp: 6.15 KB (0.0914%)
react-immutable-pure-component: 6.01 KB (0.0893%)
redux-immutable: 5.43 KB (0.0808%)
process: 5.29 KB (0.0787%)
drange: 4.8 KB (0.0714%)
lowlight: 4.42 KB (0.0658%)
scheduler: 4.33 KB (0.0644%)
deep-extend: 4.19 KB (0.0624%)
@babel/runtime: 3.99 KB (0.0594%)
deepmerge: 3.95 KB (0.0588%)
base64-js: 3.84 KB (0.0571%)
stream-browserify: 3.76 KB (0.0559%)
which-typed-array: 3.64 KB (0.0541%)
@swagger-api/apidom-error: 3.45 KB (0.0513%)
copy-to-clipboard: 3.29 KB (0.0489%)
format: 3.26 KB (0.0484%)
is-callable: 3.15 KB (0.0468%)
css.escape: 3.08 KB (0.0458%)
to-buffer: 3.03 KB (0.0451%)
serialize-error: 2.93 KB (0.0435%)
use-sync-external-store: 2.81 KB (0.0418%)
prop-types: 2.6 KB (0.0387%)
querystringify: 2.5 KB (0.0372%)
for-each: 2.32 KB (0.0345%)
has-symbols: 2.31 KB (0.0344%)
define-data-property: 2.28 KB (0.0339%)
xml-but-prettier: 2.17 KB (0.0322%)
function-bind: 2.12 KB (0.0315%)
ieee754: 2.1 KB (0.0313%)
safe-buffer: 1.63 KB (0.0243%)
util-deprecate: 1.58 KB (0.0234%)
randombytes: 1.54 KB (0.0229%)
js-file-download: 1.52 KB (0.0226%)
classnames: 1.49 KB (0.0222%)
call-bind-apply-helpers: 1.35 KB (0.0201%)
set-function-length: 1.24 KB (0.0185%)
repeat-string: 1.18 KB (0.0176%)
get-proto: 1.1 KB (0.0164%)
dunder-proto: 980 B (0.0142%)
@babel/runtime-corejs3: 781 B (0.0113%)
math-intrinsics: 781 B (0.0113%)
toggle-selection: 780 B (0.0113%)
requires-port: 753 B (0.0109%)
inherits: 753 B (0.0109%)
@swagger-api/apidom-json-pointer: 709 B (0.0103%)
fault: 691 B (0.0100%)
call-bound: 667 B (0.00969%)
call-bind: 643 B (0.00934%)
typed-array-buffer: 591 B (0.00858%)
has-property-descriptors: 588 B (0.00854%)
es-errors: 524 B (0.00761%)
available-typed-arrays: 475 B (0.00690%)
gopd: 303 B (0.00440%)
es-define-property: 288 B (0.00418%)
possible-typed-array-names: 264 B (0.00383%)
hasown: 206 B (0.00299%)
has-tostringtag: 189 B (0.00275%)
is-typed-array: 180 B (0.00261%)
isarray: 132 B (0.00192%)
es-object-atoms: 67 B (0.000973%)
<self>: 3.08 MB (46.9%)
