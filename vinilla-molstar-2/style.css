/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* 上传区域样式 */
.upload-section {
    margin-bottom: 20px;
}

.upload-area {
    border: 3px dashed #fff;
    border-radius: 15px;
    padding: 40px;
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: #4CAF50;
}

.upload-area.dragover {
    background: rgba(76, 175, 80, 0.2);
    border-color: #4CAF50;
}

.upload-content {
    color: white;
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.upload-content p {
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.file-types {
    font-size: 0.9rem;
    opacity: 0.8;
}

.upload-btn {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 1rem;
    cursor: pointer;
    transition: background 0.3s ease;
    margin-top: 15px;
}

.upload-btn:hover {
    background: #45a049;
}

/* 文件信息样式 */
.file-info {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
}

.file-details {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.file-name {
    font-weight: bold;
    color: #333;
}

.file-size {
    font-size: 0.9rem;
    color: #666;
}

.clear-btn {
    background: #f44336;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.clear-btn:hover {
    background: #da190b;
}

/* 控制按钮样式 */
.controls {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.control-btn {
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.control-btn:hover:not(:disabled) {
    background: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 查看器容器样式 */
.viewer-container {
    flex: 1;
    position: relative;
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    min-height: 600px;
}

.molstar-parent {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.molstar-canvas {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
}

/* 加载动画样式 */
.loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 1000;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误消息样式 */
.error-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #f44336;
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    z-index: 1001;
    max-width: 400px;
}

.error-message button {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 10px;
}

/* 信息面板样式 */
.info-panel {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    min-width: 200px;
    z-index: 100;
}

.info-panel h3 {
    margin-bottom: 15px;
    color: #333;
    border-bottom: 2px solid #4CAF50;
    padding-bottom: 5px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.info-item label {
    font-weight: 500;
    color: #666;
}

.info-item span {
    color: #333;
    font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .upload-area {
        padding: 30px 20px;
    }
    
    .controls {
        flex-direction: column;
        align-items: center;
    }
    
    .control-btn {
        width: 200px;
    }
    
    .info-panel {
        position: relative;
        top: auto;
        right: auto;
        margin-top: 20px;
    }
}
